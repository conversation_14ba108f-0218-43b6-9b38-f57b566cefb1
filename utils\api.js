/**
 * API接口管理
 */

const { showError, showLoading, hideLoading } = require('./util.js')

/**
 * 云函数调用封装
 * @param {string} name 云函数名称
 * @param {object} data 传递的数据
 * @param {boolean} showLoad 是否显示加载提示
 * @returns {Promise} 调用结果
 */
const callCloudFunction = async (name, data = {}, showLoad = false) => {
  if (showLoad) {
    showLoading()
  }

  try {
    const result = await wx.cloud.callFunction({
      name,
      data
    })

    if (showLoad) {
      hideLoading()
    }

    if (result.result && result.result.success) {
      return result.result.data
    } else {
      const errorMsg = result.result?.message || '请求失败'
      showError(errorMsg)
      throw new Error(errorMsg)
    }
  } catch (error) {
    if (showLoad) {
      hideLoading()
    }
    console.error(`云函数 ${name} 调用失败:`, error)
    showError('网络请求失败，请稍后重试')
    throw error
  }
}

/**
 * 数据库查询封装
 * @param {string} collection 集合名称
 * @param {object} options 查询选项
 * @returns {Promise} 查询结果
 */
const dbQuery = async (collection, options = {}) => {
  try {
    const db = wx.cloud.database()
    let query = db.collection(collection)

    // 添加查询条件
    if (options.where) {
      query = query.where(options.where)
    }

    // 添加排序
    if (options.orderBy) {
      query = query.orderBy(options.orderBy.field, options.orderBy.order || 'asc')
    }

    // 添加限制
    if (options.limit) {
      query = query.limit(options.limit)
    }

    // 添加跳过
    if (options.skip) {
      query = query.skip(options.skip)
    }

    // 添加字段选择
    if (options.field) {
      query = query.field(options.field)
    }

    const result = await query.get()
    return result.data
  } catch (error) {
    console.error('数据库查询失败:', error)
    throw error
  }
}

// 用户相关API
const userAPI = {
  // 用户登录
  login: () => callCloudFunction('login', {}, true),

  // 获取用户信息
  getUserInfo: (openid) => callCloudFunction('getUserInfo', { openid }),

  // 更新用户信息
  updateUserInfo: (userInfo) => callCloudFunction('updateUserInfo', userInfo),

  // 获取用户积分
  getUserPoints: (openid) => callCloudFunction('getUserPoints', { openid }),

  // 获取积分流水
  getPointsLog: (openid, page = 1, limit = 20) => 
    callCloudFunction('getPointsLog', { openid, page, limit })
}

// 资料相关API
const materialAPI = {
  // 获取资料列表
  getMaterialList: (params) => callCloudFunction('getMaterialList', params, true),

  // 获取资料详情
  getMaterialDetail: (materialId) => callCloudFunction('getMaterialDetail', { materialId }),

  // 搜索资料
  searchMaterials: (keyword, page = 1, limit = 20) =>
    callCloudFunction('searchMaterials', { keyword, page, limit }, true),

  // 下载资料
  downloadMaterial: (materialId) => callCloudFunction('downloadMaterial', { materialId }, true),

  // 获取推荐资料
  getRecommendMaterials: (limit = 10) =>
    callCloudFunction('getRecommendMaterials', { limit }),

  // 收藏相关方法
  addFavorite: (materialId) => callCloudFunction('manageFavorite', { materialId, action: 'add' }),
  removeFavorite: (materialId) => callCloudFunction('manageFavorite', { materialId, action: 'remove' }),
  checkFavoriteStatus: (materialId) => callCloudFunction('manageFavorite', { materialId, action: 'check' }),

  // 下载状态检查
  checkDownloadStatus: async (materialId) => {
    try {
      // 这里可以调用专门的云函数检查下载状态
      // 暂时返回false，后续可以实现
      return false
    } catch (error) {
      console.error('检查下载状态失败:', error)
      return false
    }
  },

  // 获取我的下载记录
  getMyDownloads: (params) => callCloudFunction('getMyDownloads', params),

  // 删除下载记录
  deleteDownloadRecord: (downloadId) => callCloudFunction('deleteDownloadRecord', { downloadId }),

  // 清空下载历史
  clearDownloadHistory: () => callCloudFunction('clearDownloadHistory', {}),

  // 获取我的收藏
  getMyFavorites: (params) => callCloudFunction('getMyFavorites', params)
}

// 分类相关API
const categoryAPI = {
  // 获取所有分类
  getCategories: () => callCloudFunction('getCategories'),

  // 获取分类选项
  getCategoryOptions: (categoryId) => 
    callCloudFunction('getCategoryOptions', { categoryId })
}

// 收藏相关API
const favoriteAPI = {
  // 添加收藏
  addFavorite: (materialId) => callCloudFunction('addFavorite', { materialId }),

  // 取消收藏
  removeFavorite: (materialId) => callCloudFunction('removeFavorite', { materialId }),

  // 获取收藏列表
  getFavoriteList: (page = 1, limit = 20) => 
    callCloudFunction('getFavoriteList', { page, limit }),

  // 检查是否已收藏
  checkFavorite: (materialId) => callCloudFunction('checkFavorite', { materialId })
}

// 下载历史相关API
const downloadAPI = {
  // 获取下载历史
  getDownloadHistory: (page = 1, limit = 20) => 
    callCloudFunction('getDownloadHistory', { page, limit }),

  // 检查是否已下载
  checkDownloaded: (materialId) => callCloudFunction('checkDownloaded', { materialId })
}

// 积分相关API
const pointsAPI = {
  // 观看广告获取积分
  earnPointsByAd: () => callCloudFunction('earnPointsByAd', {}, true),

  // 分享获取积分
  earnPointsByShare: (shareCode) => callCloudFunction('earnPointsByShare', { shareCode }),

  // 签到获取积分
  earnPointsByCheckin: () => callCloudFunction('earnPointsByCheckin', {}, true),

  // 生成分享码
  generateShareCode: () => callCloudFunction('generateShareCode'),

  // 处理分享邀请
  handleShareInvite: (shareCode) => callCloudFunction('handleShareInvite', { shareCode })
}

// 配置相关API
const configAPI = {
  // 获取系统配置
  getConfig: () => callCloudFunction('getConfig'),

  // 获取积分规则
  getPointsRules: () => callCloudFunction('getPointsRules')
}

module.exports = {
  callCloudFunction,
  dbQuery,
  userAPI,
  materialAPI,
  categoryAPI,
  favoriteAPI,
  downloadAPI,
  pointsAPI,
  configAPI
}
