<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-preview {
            width: 48px;
            height: 48px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .icon-preview svg {
            width: 32px;
            height: 32px;
        }
        .icon-name {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #E55A2B;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .tab-icons {
            border-top: 2px solid #FF6B35;
            margin-top: 30px;
            padding-top: 20px;
        }
        .grade-icons {
            border-top: 2px solid #4CAF50;
            margin-top: 30px;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>小程序图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击下方的"下载"按钮可以下载对应的PNG图标</li>
                <li>Tab Bar图标会生成81x81px的尺寸</li>
                <li>功能图标会生成48x48px的尺寸</li>
                <li>年级图标会生成120x120px的尺寸</li>
                <li>下载后将图片放入小程序的images目录中</li>
            </ol>
        </div>

        <h2>功能图标</h2>
        <div class="icon-grid" id="function-icons">
            <!-- 功能图标将在这里生成 -->
        </div>

        <div class="tab-icons">
            <h2>Tab Bar图标</h2>
            <div class="icon-grid" id="tab-icons">
                <!-- Tab Bar图标将在这里生成 -->
            </div>
        </div>

        <div class="grade-icons">
            <h2>年级图标</h2>
            <div class="icon-grid" id="grade-icons">
                <!-- 年级图标将在这里生成 -->
            </div>
        </div>
    </div>

    <script>
        // 图标配置
        const iconConfigs = {
            function: [
                { id: 'search', name: 'icon-search.png', size: 48, color: '#666' },
                { id: 'arrow-right', name: 'icon-arrow-right.png', size: 48, color: '#666' },
                { id: 'arrow-down', name: 'icon-arrow-down.png', size: 48, color: '#666' },
                { id: 'star', name: 'icon-star.png', size: 48, color: '#666' },
                { id: 'star-filled', name: 'icon-star-filled.png', size: 48, color: '#FF6B35' },
                { id: 'share', name: 'icon-share.png', size: 48, color: '#666' },
                { id: 'download', name: 'icon-download.png', size: 48, color: '#666' },
                { id: 'points', name: 'icon-points.png', size: 48, color: '#FF6B35' },
                { id: 'close', name: 'icon-close.png', size: 48, color: '#666' },
                { id: 'check', name: 'icon-check.png', size: 48, color: '#4CAF50' },
                { id: 'earn', name: 'icon-earn.png', size: 48, color: '#FF6B35' },
                { id: 'service', name: 'icon-service.png', size: 48, color: '#666' },
                { id: 'help', name: 'icon-help.png', size: 48, color: '#666' }
            ],
            tab: [
                { id: 'home', name: 'tab-home.png', size: 81, color: '#999' },
                { id: 'home', name: 'tab-home-active.png', size: 81, color: '#FF6B35' },
                { id: 'category', name: 'tab-category.png', size: 81, color: '#999' },
                { id: 'category', name: 'tab-category-active.png', size: 81, color: '#FF6B35' },
                { id: 'profile', name: 'tab-profile.png', size: 81, color: '#999' },
                { id: 'profile', name: 'tab-profile-active.png', size: 81, color: '#FF6B35' }
            ],
            grade: [
                { id: 'grade-1', name: 'grade-grade_1.png', size: 120, color: '#FF6B35' },
                { id: 'grade-2', name: 'grade-grade_2.png', size: 120, color: '#FF6B35' },
                { id: 'grade-3', name: 'grade-grade_3.png', size: 120, color: '#FF6B35' },
                { id: 'grade-4', name: 'grade-grade_4.png', size: 120, color: '#FF6B35' },
                { id: 'grade-5', name: 'grade-grade_5.png', size: 120, color: '#FF6B35' },
                { id: 'grade-6', name: 'grade-grade_6.png', size: 120, color: '#FF6B35' }
            ]
        };

        // 创建SVG图标
        function createSVGIcon(iconId, size, color) {
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', size);
            svg.setAttribute('height', size);
            svg.setAttribute('viewBox', '0 0 24 24');
            svg.style.color = color;

            const use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
            use.setAttributeNS('http://www.w3.org/1999/xlink', 'href', `#${iconId}`);
            svg.appendChild(use);

            return svg;
        }

        // 下载图标为PNG
        function downloadIcon(iconId, fileName, size, color) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;

            // 创建SVG
            const svg = createSVGIcon(iconId, size, color);
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            const svgUrl = URL.createObjectURL(svgBlob);

            const img = new Image();
            img.onload = function() {
                ctx.fillStyle = 'transparent';
                ctx.fillRect(0, 0, size, size);
                ctx.drawImage(img, 0, 0, size, size);
                
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/png');
                
                URL.revokeObjectURL(svgUrl);
            };
            img.src = svgUrl;
        }

        // 生成图标网格
        function generateIconGrid(containerId, icons) {
            const container = document.getElementById(containerId);
            
            icons.forEach(icon => {
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                const svg = createSVGIcon(icon.id, 32, icon.color);
                preview.appendChild(svg);
                
                const name = document.createElement('div');
                name.className = 'icon-name';
                name.textContent = icon.name;
                
                const btn = document.createElement('button');
                btn.className = 'download-btn';
                btn.textContent = '下载';
                btn.onclick = () => downloadIcon(icon.id, icon.name, icon.size, icon.color);
                
                item.appendChild(preview);
                item.appendChild(name);
                item.appendChild(btn);
                container.appendChild(item);
            });
        }

        // 页面加载完成后生成图标
        document.addEventListener('DOMContentLoaded', function() {
            // 加载SVG图标库
            fetch('./icons.svg')
                .then(response => response.text())
                .then(svgContent => {
                    const div = document.createElement('div');
                    div.innerHTML = svgContent;
                    document.body.appendChild(div);
                    
                    // 生成各类图标
                    generateIconGrid('function-icons', iconConfigs.function);
                    generateIconGrid('tab-icons', iconConfigs.tab);
                    generateIconGrid('grade-icons', iconConfigs.grade);
                })
                .catch(error => {
                    console.error('加载SVG图标失败:', error);
                    alert('加载图标失败，请确保icons.svg文件存在');
                });
        });
    </script>
</body>
</html>
