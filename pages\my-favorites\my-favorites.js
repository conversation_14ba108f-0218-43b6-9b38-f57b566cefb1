// pages/my-favorites/my-favorites.js
const { materialAPI, userAPI } = require('../../utils/api.js')
const { showError, showSuccess, showConfirm, showLoading, hideLoading } = require('../../utils/util.js')

Page({
  data: {
    // 收藏列表
    favorites: [],

    // 分页信息
    currentPage: 1,
    hasMore: true,

    // 状态控制
    loading: true,
    loadingMore: false,

    // 筛选和排序
    sortType: 'time', // time, name, subject
    sortOptions: [
      { value: 'time', name: '收藏时间' },
      { value: 'name', name: '资料名称' },
      { value: 'subject', name: '科目分类' }
    ],
    showSortMenu: false,

    // 统计信息
    totalFavorites: 0,

    // 用户信息
    userInfo: null,
    isLogin: false,

    // 编辑模式
    editMode: false,
    selectedItems: []
  },

  onLoad(options) {
    this.checkUserStatus()
  },

  onShow() {
    this.checkUserStatus()
    if (this.data.isLogin) {
      this.refreshFavorites()
    }
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore && this.data.isLogin) {
      this.loadMoreFavorites()
    }
  },

  onPullDownRefresh() {
    if (this.data.isLogin) {
      this.refreshFavorites().finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  // 检查用户状态
  checkUserStatus() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    const isLogin = app.globalData.isLogin

    this.setData({
      userInfo,
      isLogin
    })

    if (isLogin) {
      this.loadFavorites()
    } else {
      this.setData({ loading: false })
    }
  },

  // 加载收藏列表
  async loadFavorites() {
    if (!this.data.isLogin) return

    try {
      this.setData({ loading: true })

      const params = {
        page: 1,
        limit: 20,
        sortType: this.data.sortType
      }

      const result = await materialAPI.getMyFavorites(params)

      this.setData({
        favorites: result.favorites || [],
        currentPage: 1,
        hasMore: result.hasMore || false,
        totalFavorites: result.total || 0
      })

    } catch (error) {
      console.error('加载收藏列表失败:', error)
      showError('加载失败，请稍后重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载更多收藏记录
  async loadMoreFavorites() {
    if (this.data.loadingMore || !this.data.hasMore || !this.data.isLogin) return

    try {
      this.setData({ loadingMore: true })

      const nextPage = this.data.currentPage + 1
      const params = {
        page: nextPage,
        limit: 20,
        sortType: this.data.sortType
      }

      const result = await materialAPI.getMyFavorites(params)

      this.setData({
        favorites: [...this.data.favorites, ...(result.favorites || [])],
        currentPage: nextPage,
        hasMore: result.hasMore || false
      })

    } catch (error) {
      console.error('加载更多失败:', error)
      showError('加载失败')
    } finally {
      this.setData({ loadingMore: false })
    }
  },

  // 刷新收藏列表
  async refreshFavorites() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      editMode: false,
      selectedItems: []
    })
    await this.loadFavorites()
  },

  // 点击收藏项
  onFavoriteTap(e) {
    if (this.data.editMode) {
      this.onSelectItem(e)
      return
    }

    const favorite = e.currentTarget.dataset.favorite
    if (!favorite || !favorite.material) return

    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${favorite.material._id}`
    })
  },

  // 取消收藏
  async onRemoveFavorite(e) {
    const favorite = e.currentTarget.dataset.favorite
    if (!favorite || !favorite.material) return

    const confirmed = await showConfirm(`确定要取消收藏"${favorite.material.title}"吗？`, '取消收藏')
    if (!confirmed) return

    try {
      await materialAPI.removeFavorite(favorite.material._id)

      // 从列表中移除
      const favorites = this.data.favorites.filter(item => item._id !== favorite._id)
      this.setData({
        favorites,
        totalFavorites: this.data.totalFavorites - 1
      })

      showSuccess('取消收藏成功')

    } catch (error) {
      console.error('取消收藏失败:', error)
      showError('操作失败，请稍后重试')
    }
  },

  // 显示排序菜单
  onSortTap() {
    this.setData({
      showSortMenu: !this.data.showSortMenu
    })
  },

  // 选择排序方式
  onSortSelect(e) {
    const sortType = e.currentTarget.dataset.sort

    this.setData({
      sortType,
      showSortMenu: false
    })

    // 重新加载数据
    this.refreshFavorites()
  },

  // 关闭排序菜单
  onCloseSortMenu() {
    this.setData({
      showSortMenu: false
    })
  },

  // 切换编辑模式
  onToggleEditMode() {
    this.setData({
      editMode: !this.data.editMode,
      selectedItems: []
    })
  },

  // 选择项目
  onSelectItem(e) {
    const favorite = e.currentTarget.dataset.favorite
    if (!favorite) return

    const selectedItems = [...this.data.selectedItems]
    const index = selectedItems.findIndex(item => item._id === favorite._id)

    if (index > -1) {
      selectedItems.splice(index, 1)
    } else {
      selectedItems.push(favorite)
    }

    this.setData({ selectedItems })
  },

  // 全选/取消全选
  onToggleSelectAll() {
    const { favorites, selectedItems } = this.data

    if (selectedItems.length === favorites.length) {
      // 取消全选
      this.setData({ selectedItems: [] })
    } else {
      // 全选
      this.setData({ selectedItems: [...favorites] })
    }
  },

  // 批量删除收藏
  async onBatchRemove() {
    const { selectedItems } = this.data

    if (selectedItems.length === 0) {
      showError('请选择要删除的收藏')
      return
    }

    const confirmed = await showConfirm(`确定要删除选中的 ${selectedItems.length} 个收藏吗？`, '批量删除')
    if (!confirmed) return

    try {
      showLoading('删除中...')

      // 批量删除
      const promises = selectedItems.map(item =>
        materialAPI.removeFavorite(item.material._id)
      )

      await Promise.all(promises)

      // 从列表中移除
      const selectedIds = selectedItems.map(item => item._id)
      const favorites = this.data.favorites.filter(item =>
        !selectedIds.includes(item._id)
      )

      this.setData({
        favorites,
        totalFavorites: this.data.totalFavorites - selectedItems.length,
        selectedItems: [],
        editMode: false
      })

      showSuccess('删除成功')

    } catch (error) {
      console.error('批量删除失败:', error)
      showError('删除失败，请稍后重试')
    } finally {
      hideLoading()
    }
  },

  // 去登录
  onGoLogin() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 去浏览资料
  onGoBrowse() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '我的收藏',
      path: '/pages/my-favorites/my-favorites',
      imageUrl: '/images/share-cover.svg'
    }
  }
})