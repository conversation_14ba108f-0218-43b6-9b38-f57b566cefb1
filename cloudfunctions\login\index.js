// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext

  try {
    // 查询用户是否已存在
    const userResult = await db.collection('users').where({
      _openid: OPENID
    }).get()

    let userInfo = null

    if (userResult.data.length === 0) {
      // 新用户，创建用户记录
      const config = await getConfig()
      const newUserPoints = config.new_user_points || 50

      const createResult = await db.collection('users').add({
        data: {
          _openid: OPENID,
          nickName: '新用户',
          avatarUrl: '',
          points: newUserPoints,
          createTime: new Date()
        }
      })

      // 记录积分流水
      await db.collection('points_log').add({
        data: {
          user_openid: OPENID,
          amount: newUserPoints,
          balance: newUserPoints,
          type: 'new_user_reward',
          description: '新用户注册奖励',
          createTime: new Date()
        }
      })

      userInfo = {
        _id: createResult._id,
        _openid: OPENID,
        nickName: '新用户',
        avatarUrl: '',
        points: newUserPoints,
        createTime: new Date(),
        isNewUser: true
      }
    } else {
      // 老用户，返回用户信息
      userInfo = userResult.data[0]
      userInfo.isNewUser = false
    }

    return {
      success: true,
      data: userInfo
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败，请稍后重试'
    }
  }
}

// 获取系统配置
async function getConfig() {
  try {
    const result = await db.collection('config').doc('main').get()
    return result.data || {}
  } catch (error) {
    console.error('获取配置失败:', error)
    return {
      new_user_points: 50,
      enable_ad_reward: false,
      enable_share_reward: true
    }
  }
}
