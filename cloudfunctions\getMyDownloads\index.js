// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { page = 1, limit = 20, sortType = 'time' } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }

    // 查询用户信息
    const userResult = await db.collection('users')
      .where({
        openid: openid
      })
      .get()

    if (!userResult.data || userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userResult.data[0]
    const skip = (page - 1) * limit

    // 构建排序条件
    let orderBy = ['download_time', 'desc']
    if (sortType === 'name') {
      orderBy = ['material.title', 'asc']
    } else if (sortType === 'points') {
      orderBy = ['points_cost', 'desc']
    }

    // 查询下载记录总数
    const countResult = await db.collection('user_downloads')
      .where({
        user_id: user._id
      })
      .count()

    const total = countResult.total

    // 查询下载记录列表
    const downloadsResult = await db.collection('user_downloads')
      .where({
        user_id: user._id
      })
      .orderBy(orderBy[0], orderBy[1])
      .skip(skip)
      .limit(limit)
      .get()

    const downloads = downloadsResult.data

    // 获取关联的资料信息
    const materialIds = downloads.map(download => download.material_id)
    let materials = []
    
    if (materialIds.length > 0) {
      const materialsResult = await db.collection('materials')
        .where({
          _id: db.command.in(materialIds)
        })
        .get()
      
      materials = materialsResult.data
    }

    // 获取分类信息
    const categoriesResult = await db.collection('categories').get()
    const categories = categoriesResult.data

    // 创建分类映射
    const categoryMap = {}
    categories.forEach(category => {
      if (category.options) {
        category.options.forEach(option => {
          categoryMap[option.id] = {
            name: option.name,
            categoryName: category.name
          }
        })
      }
    })

    // 创建资料映射
    const materialMap = {}
    materials.forEach(material => {
      // 为资料添加分类名称
      const categoryNames = {}
      if (material.category_ids && material.category_ids.length > 0) {
        material.category_ids.forEach(categoryId => {
          const categoryInfo = categoryMap[categoryId]
          if (categoryInfo) {
            if (categoryId.startsWith('grade_')) {
              categoryNames.grade_name = categoryInfo.name
            } else if (['chinese', 'math', 'english', 'science', 'morality'].includes(categoryId)) {
              categoryNames.subject_name = categoryInfo.name
            } else if (categoryId.includes('semester')) {
              categoryNames.semester_name = categoryInfo.name
            } else if (['renjiao', 'beijing', 'sujiao', 'jiangsu', 'shanghai'].includes(categoryId)) {
              categoryNames.textbook_name = categoryInfo.name
            }
          }
        })
      }

      materialMap[material._id] = {
        ...material,
        ...categoryNames
      }
    })

    // 组合下载记录和资料信息
    const enrichedDownloads = downloads.map(download => {
      const material = materialMap[download.material_id]
      
      // 格式化下载时间
      let download_time_formatted = ''
      if (download.download_time) {
        const date = new Date(download.download_time)
        download_time_formatted = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
      }

      return {
        ...download,
        material: material || null,
        download_time_formatted
      }
    }).filter(download => download.material) // 过滤掉资料不存在的记录

    // 计算统计信息
    const totalPointsResult = await db.collection('user_downloads')
      .where({
        user_id: user._id
      })
      .get()

    const totalPoints = totalPointsResult.data.reduce((sum, download) => {
      return sum + (download.points_cost || 0)
    }, 0)

    // 计算是否还有更多数据
    const hasMore = skip + downloads.length < total

    return {
      success: true,
      data: {
        downloads: enrichedDownloads,
        total,
        totalPoints,
        page,
        limit,
        hasMore
      }
    }

  } catch (error) {
    console.error('获取下载记录失败:', error)
    return {
      success: false,
      message: '获取下载记录失败'
    }
  }
}
