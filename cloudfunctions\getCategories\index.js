// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 获取所有分类，按排序权重排序
    const result = await db.collection('categories')
      .orderBy('sort_order', 'asc')
      .get()

    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取分类失败:', error)
    return {
      success: false,
      message: '获取分类失败'
    }
  }
}
