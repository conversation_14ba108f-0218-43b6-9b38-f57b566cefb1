// pages/category/category.js
const { categoryAPI } = require('../../utils/api.js')
const { showError } = require('../../utils/util.js')

Page({
  data: {
    categories: [],
    loading: true,
    selectedFilters: {},
    filterVisible: false,
    currentFilterType: '',
    currentFilterCategory: null,
    currentFilterOptions: []
  },

  onLoad() {
    this.loadCategories()
  },

  onPullDownRefresh() {
    this.loadCategories().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载分类数据
  async loadCategories() {
    try {
      this.setData({ loading: true })

      const categories = await categoryAPI.getCategories()
      this.setData({
        categories: categories || []
      })
    } catch (error) {
      console.error('加载分类失败:', error)
      showError('加载分类失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 点击筛选器
  onFilterTap(e) {
    const { type } = e.currentTarget.dataset
    const category = this.data.categories.find(cat => cat._id === type)

    if (!category) return

    this.setData({
      currentFilterType: type,
      currentFilterCategory: category,
      currentFilterOptions: category.options || [],
      filterVisible: true
    })
  },

  // 选择筛选选项
  onFilterOptionTap(e) {
    const { option } = e.currentTarget.dataset
    const { currentFilterType } = this.data

    this.setData({
      [`selectedFilters.${currentFilterType}`]: option,
      filterVisible: false
    })
  },

  // 关闭筛选弹窗
  onFilterClose() {
    this.setData({
      filterVisible: false,
      currentFilterCategory: null,
      currentFilterOptions: []
    })
  },

  // 重置筛选条件
  onResetFilters() {
    this.setData({
      selectedFilters: {}
    })
  },

  // 应用筛选条件
  onApplyFilters() {
    const filters = this.data.selectedFilters
    const filterParams = Object.keys(filters).map(key => `${key}=${filters[key].id}`).join('&')

    wx.navigateTo({
      url: `/pages/material-list/material-list?${filterParams}&title=筛选结果`
    })
  },

  // 快速分类导航
  onQuickCategory(e) {
    const { category, option } = e.currentTarget.dataset

    wx.navigateTo({
      url: `/pages/material-list/material-list?${category}=${option.id}&title=${option.name}`
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '小学教辅资料分类',
      path: '/pages/category/category',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})