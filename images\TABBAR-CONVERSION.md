
# TabBar图标转换说明

## 问题
微信小程序的TabBar不支持SVG格式图标，只支持PNG和JPG格式。

## 解决方案

### 方法1：在线转换（推荐）
1. 访问：https://convertio.co/svg-png/
2. 上传生成的SVG文件：
   - tab-home.svg → tab-home.png
   - tab-home-active.svg → tab-home-active.png
   - tab-category.svg → tab-category.png
   - tab-category-active.svg → tab-category-active.png
   - tab-profile.svg → tab-profile.png
   - tab-profile-active.svg → tab-profile-active.png
3. 设置输出尺寸为81x81像素
4. 下载PNG文件并放入images目录

### 方法2：使用图像编辑软件
1. 用Photoshop、GIMP等软件打开SVG文件
2. 设置画布尺寸为81x81像素
3. 导出为PNG格式，确保背景透明

### 方法3：使用Node.js库（需要安装依赖）
```bash
npm install canvas
```

然后运行转换脚本。

## 临时解决方案

如果暂时无法转换，可以：
1. 先使用纯色方块作为TabBar图标
2. 或者暂时注释掉TabBar配置
3. 完成功能开发后再处理图标

## 验证方法

转换完成后：
1. 确保PNG文件大小合理（建议<50KB）
2. 在微信开发者工具中预览TabBar效果
3. 检查图标在不同设备上的显示效果
