// pages/search/search.js
const { materialAPI } = require('../../utils/api.js')
const { showError, debounce } = require('../../utils/util.js')

Page({
  data: {
    // 搜索相关
    keyword: '',
    searchResults: [],
    searchHistory: [],
    hotKeywords: ['数学试卷', '语文练习', '英语单词', '期末复习', '课后作业'],

    // 状态控制
    searching: false,
    hasSearched: false,
    showHistory: true,

    // 分页
    currentPage: 1,
    hasMore: true,
    loadingMore: false
  },

  onLoad(options) {
    // 处理从其他页面传入的搜索关键词
    if (options.keyword) {
      const keyword = decodeURIComponent(options.keyword)
      this.setData({
        keyword,
        showHistory: false
      })
      this.performSearch(keyword)
    }

    this.loadSearchHistory()
  },

  onShow() {
    // 页面显示时聚焦搜索框
    this.focusSearchInput()
  },

  onReachBottom() {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loadingMore && this.data.hasSearched) {
      this.loadMoreResults()
    }
  },

  onPullDownRefresh() {
    if (this.data.hasSearched) {
      this.refreshSearch()
    } else {
      wx.stopPullDownRefresh()
    }
  },

  // 聚焦搜索输入框
  focusSearchInput() {
    setTimeout(() => {
      this.selectComponent('#search-input')?.focus()
    }, 100)
  },

  // 搜索输入处理
  onSearchInput: debounce(function(e) {
    const keyword = e.detail.value.trim()
    this.setData({
      keyword,
      showHistory: !keyword
    })
  }, 300),

  // 搜索提交
  onSearchSubmit(e) {
    let keyword = ''

    if (e.detail && e.detail.value) {
      // 来自输入框
      keyword = e.detail.value.trim()
    } else if (e.currentTarget && e.currentTarget.dataset.keyword) {
      // 来自历史记录或热门搜索
      keyword = e.currentTarget.dataset.keyword
    } else {
      keyword = this.data.keyword.trim()
    }

    if (!keyword) {
      showError('请输入搜索关键词')
      return
    }

    this.setData({
      keyword,
      showHistory: false
    })

    this.performSearch(keyword)
  },

  // 执行搜索
  async performSearch(keyword) {
    if (this.data.searching) return

    try {
      this.setData({
        searching: true,
        hasSearched: false,
        currentPage: 1,
        hasMore: true
      })

      const results = await materialAPI.searchMaterials(keyword, 1, 20)

      this.setData({
        searchResults: results.materials || [],
        hasSearched: true,
        hasMore: results.hasMore || false,
        currentPage: 1
      })

      // 保存搜索历史
      this.saveSearchHistory(keyword)

    } catch (error) {
      console.error('搜索失败:', error)
      showError('搜索失败，请稍后重试')
    } finally {
      this.setData({
        searching: false
      })
    }
  },

  // 加载更多搜索结果
  async loadMoreResults() {
    if (this.data.loadingMore || !this.data.hasMore) return

    try {
      this.setData({ loadingMore: true })

      const nextPage = this.data.currentPage + 1
      const results = await materialAPI.searchMaterials(this.data.keyword, nextPage, 20)

      this.setData({
        searchResults: [...this.data.searchResults, ...(results.materials || [])],
        currentPage: nextPage,
        hasMore: results.hasMore || false
      })

    } catch (error) {
      console.error('加载更多失败:', error)
      showError('加载失败')
    } finally {
      this.setData({ loadingMore: false })
    }
  },

  // 刷新搜索结果
  async refreshSearch() {
    try {
      await this.performSearch(this.data.keyword)
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 清空搜索
  onClearSearch() {
    this.setData({
      keyword: '',
      searchResults: [],
      hasSearched: false,
      showHistory: true,
      currentPage: 1,
      hasMore: true
    })
    this.focusSearchInput()
  },

  // 点击搜索结果
  onResultTap(e) {
    const material = e.currentTarget.dataset.material
    if (!material) return

    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${material._id}`
    })
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || []
      this.setData({
        searchHistory: history.slice(0, 10) // 最多显示10条
      })
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    try {
      let history = wx.getStorageSync('searchHistory') || []

      // 移除重复项
      history = history.filter(item => item !== keyword)

      // 添加到开头
      history.unshift(keyword)

      // 限制数量
      history = history.slice(0, 20)

      wx.setStorageSync('searchHistory', history)

      this.setData({
        searchHistory: history.slice(0, 10)
      })
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  },

  // 删除搜索历史项
  onDeleteHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    let history = this.data.searchHistory.filter(item => item !== keyword)

    this.setData({
      searchHistory: history
    })

    try {
      wx.setStorageSync('searchHistory', history)
    } catch (error) {
      console.error('删除搜索历史失败:', error)
    }
  },

  // 清空搜索历史
  onClearHistory() {
    this.setData({
      searchHistory: []
    })

    try {
      wx.removeStorageSync('searchHistory')
    } catch (error) {
      console.error('清空搜索历史失败:', error)
    }
  },

  // 分享页面
  onShareAppMessage() {
    const keyword = this.data.keyword
    return {
      title: keyword ? `搜索"${keyword}"的结果` : '小学教辅资料搜索',
      path: keyword ? `/pages/search/search?keyword=${encodeURIComponent(keyword)}` : '/pages/search/search',
      imageUrl: '/images/share-cover.svg'
    }
  }
})