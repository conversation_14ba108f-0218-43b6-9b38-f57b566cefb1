// pages/material-list/material-list.js
const { materialAPI, categoryAPI } = require('../../utils/api.js')
const { showError } = require('../../utils/util.js')

Page({
  data: {
    // 页面信息
    pageTitle: '资料列表',

    // 筛选条件
    filters: {},
    categories: [],

    // 资料列表
    materials: [],

    // 分页信息
    currentPage: 1,
    hasMore: true,

    // 状态控制
    loading: true,
    loadingMore: false,

    // 排序方式
    sortType: 'default', // default, time, download, points
    sortOptions: [
      { value: 'default', name: '默认排序' },
      { value: 'time', name: '最新上传' },
      { value: 'download', name: '下载最多' },
      { value: 'points', name: '积分最少' }
    ],
    showSortMenu: false
  },

  onLoad(options) {
    // 解析页面参数
    this.parsePageOptions(options)

    // 加载分类数据
    this.loadCategories()

    // 加载资料列表
    this.loadMaterials()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.materials.length > 0) {
      this.refreshMaterials()
    }
  },

  onReachBottom() {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMoreMaterials()
    }
  },

  onPullDownRefresh() {
    this.refreshMaterials().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 解析页面参数
  parsePageOptions(options) {
    const filters = {}
    let pageTitle = '资料列表'

    // 解析筛选条件
    if (options.grade) {
      filters.grade = options.grade
    }
    if (options.subject) {
      filters.subject = options.subject
    }
    if (options.semester) {
      filters.semester = options.semester
    }
    if (options.textbook) {
      filters.textbook = options.textbook
    }
    if (options.project_type) {
      filters.project_type = options.project_type
    }

    // 设置页面标题
    if (options.title) {
      pageTitle = decodeURIComponent(options.title)
    }

    this.setData({
      filters,
      pageTitle
    })

    // 更新导航栏标题
    wx.setNavigationBarTitle({
      title: pageTitle
    })
  },

  // 加载分类数据
  async loadCategories() {
    try {
      const categories = await categoryAPI.getCategories()
      this.setData({
        categories: categories || []
      })
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  },

  // 加载资料列表
  async loadMaterials() {
    if (this.data.loading) return

    try {
      this.setData({ loading: true })

      const params = {
        ...this.data.filters,
        page: 1,
        limit: 20,
        sortType: this.data.sortType
      }

      const result = await materialAPI.getMaterialList(params)

      this.setData({
        materials: result.materials || [],
        currentPage: 1,
        hasMore: result.hasMore || false
      })

    } catch (error) {
      console.error('加载资料列表失败:', error)
      showError('加载失败，请稍后重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载更多资料
  async loadMoreMaterials() {
    if (this.data.loadingMore || !this.data.hasMore) return

    try {
      this.setData({ loadingMore: true })

      const nextPage = this.data.currentPage + 1
      const params = {
        ...this.data.filters,
        page: nextPage,
        limit: 20,
        sortType: this.data.sortType
      }

      const result = await materialAPI.getMaterialList(params)

      this.setData({
        materials: [...this.data.materials, ...(result.materials || [])],
        currentPage: nextPage,
        hasMore: result.hasMore || false
      })

    } catch (error) {
      console.error('加载更多失败:', error)
      showError('加载失败')
    } finally {
      this.setData({ loadingMore: false })
    }
  },

  // 刷新资料列表
  async refreshMaterials() {
    this.setData({
      currentPage: 1,
      hasMore: true
    })
    await this.loadMaterials()
  },

  // 点击资料项
  onMaterialTap(e) {
    const material = e.currentTarget.dataset.material
    if (!material) return

    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${material._id}`
    })
  },

  // 显示排序菜单
  onSortTap() {
    this.setData({
      showSortMenu: !this.data.showSortMenu
    })
  },

  // 选择排序方式
  onSortSelect(e) {
    const sortType = e.currentTarget.dataset.sort

    this.setData({
      sortType,
      showSortMenu: false
    })

    // 重新加载数据
    this.refreshMaterials()
  },

  // 关闭排序菜单
  onCloseSortMenu() {
    this.setData({
      showSortMenu: false
    })
  },

  // 打开筛选页面
  onFilterTap() {
    // 将当前筛选条件传递给筛选页面
    const filterParams = Object.keys(this.data.filters)
      .map(key => `${key}=${this.data.filters[key]}`)
      .join('&')

    wx.navigateTo({
      url: `/pages/category/category?${filterParams}`
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: this.data.pageTitle,
      path: `/pages/material-list/material-list?title=${encodeURIComponent(this.data.pageTitle)}`,
      imageUrl: '/images/share-cover.svg'
    }
  }
})