# 小学生教辅资料小程序

基于微信小程序开发的小学生教辅资料下载平台，采用腾讯云开发(CloudBase)作为后端服务。

## 项目概述

这是一个专为小学生设计的教辅资料下载平台，通过积分制度管理资料下载，支持分享获取积分、观看广告获取积分等多种积分获取方式。

## 技术栈

- **前端**: 微信小程序原生开发
- **后端**: 腾讯云开发 (CloudBase)
- **数据库**: 云数据库
- **存储**: 云存储
- **云函数**: Node.js

## 当前开发进度

### ✅ 已完成的功能

#### 1. 项目基础架构
- [x] 微信小程序基础配置
- [x] 云开发环境配置
- [x] 全局样式和工具函数
- [x] API接口管理封装
- [x] 数据库结构设计
- [x] **图标资源自动化生成系统**

#### 2. 核心页面
- [x] 首页 (pages/home)
  - 搜索框组件
  - 年级分类导航
  - 推荐资料展示
  - 下拉刷新功能
- [x] 个人中心页 (pages/profile)
  - 用户信息展示
  - 积分管理
  - 功能入口列表
  - 登录/授权功能
- [x] 分类页 (pages/category)
  - 多维度筛选器
  - 快速分类导航
  - 筛选弹窗

#### 3. 云函数
- [x] login - 用户登录和注册
- [x] getCategories - 获取分类数据
- [x] getRecommendMaterials - 获取推荐资料

#### 4. 数据库设计
- [x] users - 用户信息表
- [x] materials - 教辅资料表
- [x] categories - 动态分类表
- [x] config - 全局配置表
- [x] points_log - 积分流水表
- [x] user_favorites - 用户收藏表
- [x] user_downloads - 用户下载历史表
- [x] share_records - 分享记录表

#### 5. 图标资源系统
- [x] **自动化图标生成脚本** (`images/generate-icons.js`)
- [x] **完整的SVG图标库** (37个图标文件)
- [x] **Tab Bar图标** (6个，支持选中/未选中状态)
- [x] **功能图标** (13个，涵盖所有核心功能)
- [x] **年级图标** (6个，一到六年级)
- [x] **占位图片** (6个，默认封面、头像、空状态等)
- [x] **图标使用文档** (`images/USAGE.md`)
- [x] **已更新所有页面的图标引用**

### 🚧 正在开发的功能

#### 1. 已完成的页面
- [x] **搜索页** (pages/search) - 完整的搜索功能
  - 搜索输入和提交
  - 搜索历史记录
  - 热门搜索推荐
  - 搜索结果展示
  - 分页加载更多
- [x] **资料列表页** (pages/material-list) - 资料浏览功能
  - 多维度筛选
  - 排序功能
  - 分页加载
  - 跳转详情页

#### 2. 新完成的页面
- [x] **资料详情页** (pages/material-detail) - 完整的资料查看和下载功能
  - 资料信息展示
  - 预览图片浏览
  - 收藏/取消收藏
  - 积分下载功能
  - 文件保存到本地
  - 相关推荐和举报

#### 3. 新完成的用户功能页面
- [x] **我的下载页** (pages/my-downloads) - 完整的下载历史管理
  - 下载记录列表展示
  - 统计信息显示
  - 排序和筛选功能
  - 重新下载功能
  - 删除记录和批量操作
- [x] **我的收藏页** (pages/my-favorites) - 完整的收藏管理功能
  - 收藏列表展示
  - 编辑模式和批量操作
  - 排序功能
  - 取消收藏功能

#### 4. 待完成的页面
- [ ] 积分明细页 (pages/points-detail)

#### 4. 已完成的云函数
- [x] **searchMaterials** - 搜索资料功能
  - 支持标题、描述、标签搜索
  - 正则表达式匹配
  - 分页和排序
  - 分类信息关联
- [x] **getMaterialDetail** - 获取资料详情功能
  - 完整的资料信息
  - 分类名称关联
  - 浏览次数统计
  - 时间格式化
- [x] **downloadMaterial** - 下载资料功能
  - 积分扣除逻辑
  - 下载历史记录
  - 文件链接生成
  - 重复下载处理
- [x] **manageFavorite** - 收藏管理功能
  - 添加/取消收藏
  - 收藏状态检查
  - 数据验证
- [x] **getMyDownloads** - 获取用户下载记录
  - 分页查询下载历史
  - 关联资料信息
  - 统计积分消耗
  - 多种排序方式
- [x] **getMyFavorites** - 获取用户收藏记录
  - 分页查询收藏列表
  - 关联资料信息
  - 过滤无效资料
  - 时间格式化

#### 5. 待开发的云函数
- [ ] getMaterialList - 获取资料列表
- [ ] earnPointsByAd - 观看广告获取积分
- [ ] earnPointsByShare - 分享获取积分
- [ ] handleShareInvite - 处理分享邀请

#### 3. 核心功能
- [ ] 文件上传和预览图生成
- [ ] 积分系统完整实现
- [ ] 分享奖励机制
- [ ] 激励视频广告集成
- [ ] 文件下载和打开功能

### 📋 下一步开发计划

1. **完成搜索功能** (预计1天)
   - 搜索页面开发
   - 搜索云函数实现
   - 搜索历史记录

2. **完成资料列表和详情页** (预计2天)
   - 资料列表页面
   - 多维度筛选功能
   - 资料详情页面
   - 收藏和分享功能

3. **完成积分系统** (预计2天)
   - 积分获取机制
   - 积分消耗逻辑
   - 积分流水记录
   - 分享奖励系统

4. **完成文件管理** (预计2天)
   - 文件下载功能
   - 预览图生成
   - 下载历史记录

5. **测试和优化** (预计1天)
   - 功能测试
   - 性能优化
   - 错误处理完善

## 部署说明

### 环境要求
- 微信开发者工具
- 腾讯云开发环境
- Node.js 14+

### 部署步骤

1. **配置云开发环境**
   ```bash
   # 在微信开发者工具中开通云开发
   # 创建云环境并记录环境ID
   ```

2. **更新环境配置**
   ```javascript
   // 在 app.js 中更新云环境ID
   wx.cloud.init({
     env: 'your-cloud-env-id', // 替换为实际的环境ID
     traceUser: true,
   })
   ```

3. **部署云函数**
   ```bash
   # 在微信开发者工具中右键云函数目录
   # 选择"上传并部署"
   ```

4. **初始化数据库**
   ```bash
   # 在云开发控制台中导入 database/init-data.js 中的数据
   ```

5. **配置图片资源**
   ```bash
   # 根据 images/README.md 准备所需的图片资源
   ```

## 项目结构

```
wx_k12/
├── pages/                  # 页面目录
│   ├── home/              # 首页
│   ├── category/          # 分类页
│   ├── profile/           # 个人中心
│   └── ...
├── cloudfunctions/        # 云函数目录
│   ├── login/             # 登录云函数
│   ├── getCategories/     # 获取分类云函数
│   └── ...
├── utils/                 # 工具函数
│   ├── util.js           # 通用工具函数
│   └── api.js            # API接口管理
├── images/               # 图片资源
├── database/             # 数据库初始化脚本
├── app.js               # 小程序入口文件
├── app.json             # 小程序配置文件
├── app.wxss             # 全局样式文件
└── README.md            # 项目说明文档
```

## 联系方式

如有问题或建议，请联系开发团队。
