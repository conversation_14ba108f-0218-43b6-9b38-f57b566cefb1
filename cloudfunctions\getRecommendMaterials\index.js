// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { limit = 10 } = event

  try {
    // 获取推荐资料，按排序权重和上传时间排序
    const materialsResult = await db.collection('materials')
      .where({
        is_active: true
      })
      .orderBy('sort_order', 'asc')
      .orderBy('upload_time', 'desc')
      .limit(limit)
      .get()

    const materials = materialsResult.data

    // 获取分类信息，用于显示标签
    const categoriesResult = await db.collection('categories').get()
    const categories = categoriesResult.data

    // 创建分类映射
    const categoryMap = {}
    categories.forEach(category => {
      category.options.forEach(option => {
        categoryMap[option.id] = {
          name: option.name,
          categoryName: category.name
        }
      })
    })

    // 为每个资料添加分类名称
    const enrichedMaterials = materials.map(material => {
      const categoryNames = {}
      
      if (material.category_ids && material.category_ids.length > 0) {
        material.category_ids.forEach(categoryId => {
          const categoryInfo = categoryMap[categoryId]
          if (categoryInfo) {
            // 根据分类类型设置对应的名称
            if (categoryId.startsWith('grade_')) {
              categoryNames.grade_name = categoryInfo.name
            } else if (['chinese', 'math', 'english', 'science', 'morality'].includes(categoryId)) {
              categoryNames.subject_name = categoryInfo.name
            } else if (categoryId.includes('semester')) {
              categoryNames.semester_name = categoryInfo.name
            } else if (['renjiao', 'beijing', 'sujiao', 'jiangsu', 'shanghai'].includes(categoryId)) {
              categoryNames.textbook_name = categoryInfo.name
            }
          }
        })
      }

      return {
        ...material,
        ...categoryNames
      }
    })

    return {
      success: true,
      data: enrichedMaterials
    }
  } catch (error) {
    console.error('获取推荐资料失败:', error)
    return {
      success: false,
      message: '获取推荐资料失败'
    }
  }
}
