# 图标下载详细指南

## 方案1：阿里巴巴矢量图标库（最推荐）

### 步骤：
1. 访问：https://www.iconfont.cn/
2. 注册/登录账号
3. 搜索并下载以下图标：

#### Tab Bar图标（搜索关键词）
```
首页/home → tab-home.png (灰色版本)
首页/home → tab-home-active.png (橙色版本 #FF6B35)
分类/category/网格 → tab-category.png (灰色版本)
分类/category/网格 → tab-category-active.png (橙色版本)
用户/个人/profile → tab-profile.png (灰色版本)
用户/个人/profile → tab-profile-active.png (橙色版本)
```

#### 功能图标（搜索关键词）
```
搜索/放大镜 → icon-search.png
右箭头/arrow-right → icon-arrow-right.png
下箭头/arrow-down → icon-arrow-down.png
星星/收藏（空心） → icon-star.png
星星/收藏（实心） → icon-star-filled.png
分享/share → icon-share.png
下载/download → icon-download.png
积分/金币/coin → icon-points.png
关闭/close/× → icon-close.png
对勾/check/✓ → icon-check.png
赚钱/earn/钱袋 → icon-earn.png
客服/service/耳机 → icon-service.png
帮助/help/问号 → icon-help.png
```

#### 年级图标（搜索关键词）
```
数字1/一年级 → grade-grade_1.png
数字2/二年级 → grade-grade_2.png
数字3/三年级 → grade-grade_3.png
数字4/四年级 → grade-grade_4.png
数字5/五年级 → grade-grade_5.png
数字6/六年级 → grade-grade_6.png
```

### 下载设置：
- **格式**：PNG
- **尺寸**：Tab Bar(81px), 功能图标(48px), 年级图标(120px)
- **颜色**：未选中(#999999), 选中/重要(#FF6B35), 普通(#666666)

## 方案2：使用免费图标库

### Feather Icons
1. 访问：https://feathericons.com/
2. 选择图标，点击下载SVG
3. 使用在线工具转换为PNG：https://convertio.co/svg-png/

### Heroicons
1. 访问：https://heroicons.com/
2. 选择outline或solid版本
3. 复制SVG代码或下载

### Tabler Icons
1. 访问：https://tabler-icons.io/
2. 搜索图标名称
3. 下载SVG格式

## 方案3：使用我提供的SVG生成器

如果你想快速开始开发，可以：

1. 打开我创建的 `generate-icons.html`
2. 使用基础的SVG图标
3. 后续再替换为更精美的图标

## 方案4：AI生成图标（备选）

可以使用以下AI工具生成图标：
- Midjourney
- DALL-E
- Stable Diffusion
- 文心一格

提示词示例：
```
"simple line icon of home, minimalist, black and white, 48x48 pixels, transparent background"
```

## 快速批量下载脚本

如果你熟悉编程，可以使用以下Node.js脚本批量处理：

```javascript
// 需要先手动下载SVG文件，然后批量转换
const sharp = require('sharp');
const fs = require('fs');

async function convertSvgToPng(svgPath, pngPath, size) {
  await sharp(svgPath)
    .resize(size, size)
    .png()
    .toFile(pngPath);
}

// 使用示例
convertSvgToPng('home.svg', 'tab-home.png', 81);
```

## 推荐的下载顺序

1. **优先级1**：Tab Bar图标（影响导航体验）
2. **优先级2**：核心功能图标（搜索、下载、收藏等）
3. **优先级3**：年级图标（可以用数字临时替代）
4. **优先级4**：辅助图标（帮助、客服等）

## 临时解决方案

如果暂时无法获取图标，可以：
1. 使用我提供的SVG生成器
2. 用文字临时替代图标
3. 使用系统默认图标
4. 先完成功能开发，后续再美化图标

这样你就可以继续开发，不会被图标问题阻塞进度。
