
# TabBar图标修复指南

## 当前状态
- PNG文件存在但是1x1像素的占位文件
- 需要替换为81x81像素的真实图标

## 解决方案

### 方法1：使用在线转换（推荐）
1. 使用刚生成的SVG文件：
   - tab-home.svg → tab-home.png
   - tab-home-active.svg → tab-home-active.png
   - tab-category.svg → tab-category.png
   - tab-category-active.svg → tab-category-active.png
   - tab-profile.svg → tab-profile.png
   - tab-profile-active.svg → tab-profile-active.png

2. 访问：https://convertio.co/svg-png/
3. 上传SVG文件，设置输出尺寸为81x81像素
4. 下载PNG文件并替换images目录中的文件

### 方法2：使用浏览器工具
1. 打开 generate-png-icons.html
2. 点击"批量下载所有TabBar图标"
3. 将下载的文件重命名并替换

### 方法3：使用图像编辑软件
1. 用Photoshop/GIMP等打开SVG文件
2. 设置画布为81x81像素
3. 导出为PNG格式

## 验证方法
转换完成后，检查文件大小：
- 正确的PNG文件应该在1-10KB之间
- 当前的占位文件只有70字节

## 临时解决方案
如果暂时无法转换，可以：
1. 使用SVG格式（但TabBar不支持）
2. 创建简单的纯色方块图标
3. 暂时禁用TabBar图标
