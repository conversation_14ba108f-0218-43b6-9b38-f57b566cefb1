/**
 * 数据库初始化数据
 * 在云开发控制台的数据库中手动导入这些数据
 */

// categories 集合初始数据
const categoriesData = [
  {
    "_id": "grade",
    "name": "年级",
    "sort_order": 1,
    "options": [
      {"id": "grade_1", "name": "一年级", "sort_order": 1, "is_active": true},
      {"id": "grade_2", "name": "二年级", "sort_order": 2, "is_active": true},
      {"id": "grade_3", "name": "三年级", "sort_order": 3, "is_active": true},
      {"id": "grade_4", "name": "四年级", "sort_order": 4, "is_active": true},
      {"id": "grade_5", "name": "五年级", "sort_order": 5, "is_active": true},
      {"id": "grade_6", "name": "六年级", "sort_order": 6, "is_active": true}
    ]
  },
  {
    "_id": "subject",
    "name": "科目",
    "sort_order": 2,
    "options": [
      {"id": "chinese", "name": "语文", "sort_order": 1, "is_active": true},
      {"id": "math", "name": "数学", "sort_order": 2, "is_active": true},
      {"id": "english", "name": "英语", "sort_order": 3, "is_active": true},
      {"id": "science", "name": "科学", "sort_order": 4, "is_active": true},
      {"id": "morality", "name": "道德与法治", "sort_order": 5, "is_active": true}
    ]
  },
  {
    "_id": "semester",
    "name": "上下册",
    "sort_order": 3,
    "options": [
      {"id": "first_semester", "name": "上册", "sort_order": 1, "is_active": true},
      {"id": "second_semester", "name": "下册", "sort_order": 2, "is_active": true},
      {"id": "full_year", "name": "全册", "sort_order": 3, "is_active": true}
    ]
  },
  {
    "_id": "textbook",
    "name": "教材版本",
    "sort_order": 4,
    "options": [
      {"id": "renjiao", "name": "人教版", "sort_order": 1, "is_active": true},
      {"id": "beijing", "name": "北师大版", "sort_order": 2, "is_active": true},
      {"id": "sujiao", "name": "苏教版", "sort_order": 3, "is_active": true},
      {"id": "jiangsu", "name": "江苏版", "sort_order": 4, "is_active": true},
      {"id": "shanghai", "name": "沪教版", "sort_order": 5, "is_active": true}
    ]
  },
  {
    "_id": "project_type",
    "name": "项目类型",
    "sort_order": 5,
    "options": [
      {"id": "sync_exercise", "name": "单页同步", "sort_order": 1, "is_active": true},
      {"id": "knowledge_test", "name": "知识点检测", "sort_order": 2, "is_active": true},
      {"id": "unit_test", "name": "单元测试", "sort_order": 3, "is_active": true},
      {"id": "final_exam", "name": "期末试卷", "sort_order": 4, "is_active": true},
      {"id": "midterm_exam", "name": "期中试卷", "sort_order": 5, "is_active": true},
      {"id": "daily_practice", "name": "日常练习", "sort_order": 6, "is_active": true},
      {"id": "homework", "name": "课后作业", "sort_order": 7, "is_active": true},
      {"id": "competition", "name": "竞赛题", "sort_order": 8, "is_active": true},
      {"id": "review_material", "name": "复习资料", "sort_order": 9, "is_active": true},
      {"id": "courseware", "name": "课件PPT", "sort_order": 10, "is_active": true}
    ]
  }
]

// config 集合初始数据
const configData = {
  "_id": "main",
  "new_user_points": 50,
  "default_material_points": 100,
  "enable_ad_reward": false,
  "ad_reward_points": 10,
  "ad_daily_limit": 5,
  "ad_interval_seconds": 30,
  "enable_share_reward": true,
  "share_reward_points": 30,
  "invited_user_points": 20,
  "enable_checkin": false,
  "checkin_rewards": [5, 5, 10, 10, 15, 15, 20],
  "min_material_points": 1,
  "max_material_points": 1000
}

// materials 集合示例数据
const materialsData = [
  {
    "_id": "material_001",
    "title": "三年级数学上册期末测试卷（人教版）",
    "description": "本试卷涵盖三年级数学上册全部知识点，包括加减法、乘除法、图形认识等内容，适合期末复习使用。",
    "points_cost": 80,
    "file_id": "cloud://env-xxx.xxx/materials/math_grade3_final.pdf",
    "file_size": 2048576,
    "file_format": "PDF",
    "cover_image": null,
    "preview_images": [],
    "category_ids": ["grade_3", "math", "first_semester", "renjiao", "final_exam"],
    "download_count": 156,
    "view_count": 423,
    "upload_time": new Date("2025-08-01T10:30:00.000Z"),
    "is_active": true,
    "sort_order": 100
  },
  {
    "_id": "material_002",
    "title": "二年级语文下册第三单元知识点检测（北师大版）",
    "description": "针对二年级语文下册第三单元的重点知识进行检测，包括生字词、课文理解、阅读理解等内容。",
    "points_cost": 60,
    "file_id": "cloud://env-xxx.xxx/materials/chinese_grade2_unit3.pdf",
    "file_size": 1536000,
    "file_format": "PDF",
    "cover_image": null,
    "preview_images": [],
    "category_ids": ["grade_2", "chinese", "second_semester", "beijing", "knowledge_test"],
    "download_count": 89,
    "view_count": 234,
    "upload_time": new Date("2025-08-02T14:20:00.000Z"),
    "is_active": true,
    "sort_order": 200
  },
  {
    "_id": "material_003",
    "title": "五年级英语上册Unit 1-2单页同步练习（苏教版）",
    "description": "配合苏教版五年级英语上册Unit 1-2课程的同步练习，包含词汇、语法、对话等练习题。",
    "points_cost": 40,
    "file_id": "cloud://env-xxx.xxx/materials/english_grade5_sync.pdf",
    "file_size": 1024000,
    "file_format": "PDF",
    "cover_image": null,
    "preview_images": [],
    "category_ids": ["grade_5", "english", "first_semester", "sujiao", "sync_exercise"],
    "download_count": 67,
    "view_count": 145,
    "upload_time": new Date("2025-08-02T09:15:00.000Z"),
    "is_active": true,
    "sort_order": 300
  }
]

console.log('=== Categories Data ===')
console.log(JSON.stringify(categoriesData, null, 2))

console.log('\n=== Config Data ===')
console.log(JSON.stringify(configData, null, 2))

console.log('\n=== Materials Data ===')
console.log(JSON.stringify(materialsData, null, 2))

module.exports = {
  categoriesData,
  configData,
  materialsData
}
