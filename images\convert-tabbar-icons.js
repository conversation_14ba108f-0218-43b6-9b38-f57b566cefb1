const fs = require('fs');
const path = require('path');

// TabBar图标配置 - 需要转换为PNG格式
const tabBarIcons = [
    { name: 'tab-home.png', size: 81, color: '#999999', symbol: '🏠' },
    { name: 'tab-home-active.png', size: 81, color: '#FF6B35', symbol: '🏠' },
    { name: 'tab-category.png', size: 81, color: '#999999', symbol: '📋' },
    { name: 'tab-category-active.png', size: 81, color: '#FF6B35', symbol: '📋' },
    { name: 'tab-profile.png', size: 81, color: '#999999', symbol: '👤' },
    { name: 'tab-profile-active.png', size: 81, color: '#FF6B35', symbol: '👤' }
];

// 生成简化的PNG图标（使用Canvas API的数据URL）
function generateTabBarIconDataURL(config) {
    // 创建一个简单的SVG数据URL，可以被转换为PNG
    const svg = `
        <svg width="${config.size}" height="${config.size}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="transparent"/>
            <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" 
                  font-size="${config.size * 0.6}" font-family="Arial">${config.symbol}</text>
        </svg>
    `;
    
    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
}

// 生成简化的TabBar图标（纯色方块 + 文字）
function generateSimpleTabBarIcon(config) {
    const svg = `
        <svg width="${config.size}" height="${config.size}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="transparent"/>
            <circle cx="50%" cy="50%" r="${config.size * 0.3}" fill="${config.color}" opacity="0.2"/>
            <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" 
                  font-size="${config.size * 0.4}" font-family="Arial" fill="${config.color}" font-weight="bold">
                ${config.name.includes('home') ? 'H' : config.name.includes('category') ? 'C' : 'M'}
            </text>
        </svg>
    `;
    
    return svg;
}

// 创建TabBar图标文件
function createTabBarIcons() {
    console.log('开始生成TabBar PNG图标...\n');

    tabBarIcons.forEach(config => {
        // 生成简化的SVG内容
        const svg = generateSimpleTabBarIcon(config);
        
        // 保存为SVG文件（临时用于转换）
        const svgPath = path.join(__dirname, config.name.replace('.png', '.svg'));
        fs.writeFileSync(svgPath, svg);
        
        console.log(`✓ 生成 ${config.name} (${config.size}x${config.size})`);
    });

    console.log('\nTabBar图标生成完成！');
    console.log('\n重要提示：');
    console.log('1. 微信小程序TabBar只支持PNG/JPG格式，不支持SVG');
    console.log('2. 请使用以下方法将SVG转换为PNG：');
    console.log('   - 在线转换：https://convertio.co/svg-png/');
    console.log('   - 或使用Photoshop等工具打开SVG并导出PNG');
    console.log('3. 转换完成后，将PNG文件放入images目录');
    console.log('4. 确保PNG文件尺寸为81x81像素');
}

// 生成转换说明
function generateConversionInstructions() {
    const instructions = `
# TabBar图标转换说明

## 问题
微信小程序的TabBar不支持SVG格式图标，只支持PNG和JPG格式。

## 解决方案

### 方法1：在线转换（推荐）
1. 访问：https://convertio.co/svg-png/
2. 上传生成的SVG文件：
   - tab-home.svg → tab-home.png
   - tab-home-active.svg → tab-home-active.png
   - tab-category.svg → tab-category.png
   - tab-category-active.svg → tab-category-active.png
   - tab-profile.svg → tab-profile.png
   - tab-profile-active.svg → tab-profile-active.png
3. 设置输出尺寸为81x81像素
4. 下载PNG文件并放入images目录

### 方法2：使用图像编辑软件
1. 用Photoshop、GIMP等软件打开SVG文件
2. 设置画布尺寸为81x81像素
3. 导出为PNG格式，确保背景透明

### 方法3：使用Node.js库（需要安装依赖）
\`\`\`bash
npm install canvas
\`\`\`

然后运行转换脚本。

## 临时解决方案

如果暂时无法转换，可以：
1. 先使用纯色方块作为TabBar图标
2. 或者暂时注释掉TabBar配置
3. 完成功能开发后再处理图标

## 验证方法

转换完成后：
1. 确保PNG文件大小合理（建议<50KB）
2. 在微信开发者工具中预览TabBar效果
3. 检查图标在不同设备上的显示效果
`;

    fs.writeFileSync(path.join(__dirname, 'TABBAR-CONVERSION.md'), instructions);
    console.log('\n转换说明已生成: TABBAR-CONVERSION.md');
}

// 执行生成
if (require.main === module) {
    try {
        createTabBarIcons();
        generateConversionInstructions();
    } catch (error) {
        console.error('生成TabBar图标时出错:', error);
    }
}

module.exports = { createTabBarIcons, generateConversionInstructions };
