const fs = require('fs');
const path = require('path');

// 创建一个更大的PNG文件（使用简单的方法）
function createBasicPNG(width, height, color) {
    // PNG文件头
    const pngSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
    
    // IHDR chunk
    const ihdrData = Buffer.alloc(13);
    ihdrData.writeUInt32BE(width, 0);
    ihdrData.writeUInt32BE(height, 4);
    ihdrData[8] = 8; // bit depth
    ihdrData[9] = 2; // color type (RGB)
    ihdrData[10] = 0; // compression
    ihdrData[11] = 0; // filter
    ihdrData[12] = 0; // interlace
    
    const ihdrCrc = crc32(Buffer.concat([Buffer.from('IHDR'), ihdrData]));
    const ihdrChunk = Buffer.concat([
        Buffer.from([0, 0, 0, 13]), // length
        Buffer.from('IHDR'),
        ihdrData,
        Buffer.from([ihdrCrc >> 24, (ihdrCrc >> 16) & 0xFF, (ihdrCrc >> 8) & 0xFF, ihdrCrc & 0xFF])
    ]);
    
    // 简化的IDAT chunk（纯色）
    const pixelData = Buffer.alloc(height * (width * 3 + 1));
    for (let y = 0; y < height; y++) {
        const rowStart = y * (width * 3 + 1);
        pixelData[rowStart] = 0; // filter type
        for (let x = 0; x < width; x++) {
            const pixelStart = rowStart + 1 + x * 3;
            // 根据颜色设置RGB值
            if (color === '#FF6B35') {
                pixelData[pixelStart] = 0xFF;     // R
                pixelData[pixelStart + 1] = 0x6B; // G
                pixelData[pixelStart + 2] = 0x35; // B
            } else {
                pixelData[pixelStart] = 0x99;     // R
                pixelData[pixelStart + 1] = 0x99; // G
                pixelData[pixelStart + 2] = 0x99; // B
            }
        }
    }
    
    // 压缩数据（简化版）
    const compressedData = Buffer.concat([Buffer.from([0x78, 0x9C]), pixelData, Buffer.from([0, 0, 0, 0])]);
    
    const idatCrc = crc32(Buffer.concat([Buffer.from('IDAT'), compressedData]));
    const idatChunk = Buffer.concat([
        Buffer.from([(compressedData.length >> 24) & 0xFF, (compressedData.length >> 16) & 0xFF, (compressedData.length >> 8) & 0xFF, compressedData.length & 0xFF]),
        Buffer.from('IDAT'),
        compressedData,
        Buffer.from([idatCrc >> 24, (idatCrc >> 16) & 0xFF, (idatCrc >> 8) & 0xFF, idatCrc & 0xFF])
    ]);
    
    // IEND chunk
    const iendCrc = crc32(Buffer.from('IEND'));
    const iendChunk = Buffer.concat([
        Buffer.from([0, 0, 0, 0]), // length
        Buffer.from('IEND'),
        Buffer.from([iendCrc >> 24, (iendCrc >> 16) & 0xFF, (iendCrc >> 8) & 0xFF, iendCrc & 0xFF])
    ]);
    
    return Buffer.concat([pngSignature, ihdrChunk, idatChunk, iendChunk]);
}

// 简单的CRC32实现
function crc32(data) {
    const table = [];
    for (let i = 0; i < 256; i++) {
        let c = i;
        for (let j = 0; j < 8; j++) {
            c = (c & 1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1);
        }
        table[i] = c;
    }
    
    let crc = 0xFFFFFFFF;
    for (let i = 0; i < data.length; i++) {
        crc = table[(crc ^ data[i]) & 0xFF] ^ (crc >>> 8);
    }
    return (crc ^ 0xFFFFFFFF) >>> 0;
}

// 创建简单的彩色方块PNG
function createColoredSquarePNG(size, color, letter) {
    // 创建一个简单的彩色方块
    const canvas = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="${color}" opacity="0.2"/>
        <circle cx="${size/2}" cy="${size/2}" r="${size*0.3}" fill="${color}" opacity="0.3"/>
        <text x="${size/2}" y="${size/2}" text-anchor="middle" dominant-baseline="middle" 
              font-size="${size*0.4}" font-family="Arial" font-weight="bold" fill="${color}">${letter}</text>
    </svg>`;
    
    return canvas;
}

// TabBar图标配置
const tabBarConfigs = [
    { name: 'tab-home.png', size: 81, color: '#999999', letter: 'H' },
    { name: 'tab-home-active.png', size: 81, color: '#FF6B35', letter: 'H' },
    { name: 'tab-category.png', size: 81, color: '#999999', letter: 'C' },
    { name: 'tab-category-active.png', size: 81, color: '#FF6B35', letter: 'C' },
    { name: 'tab-profile.png', size: 81, color: '#999999', letter: 'M' },
    { name: 'tab-profile-active.png', size: 81, color: '#FF6B35', letter: 'M' }
];

// 快速修复方案：创建更好的SVG文件
function quickFixTabBar() {
    console.log('快速修复TabBar图标...\n');
    
    tabBarConfigs.forEach(config => {
        // 创建更好的SVG
        const svg = createColoredSquarePNG(config.size, config.color, config.letter);
        const svgPath = path.join(__dirname, config.name.replace('.png', '-fixed.svg'));
        
        fs.writeFileSync(svgPath, svg);
        console.log(`✓ 创建 ${config.name.replace('.png', '-fixed.svg')}`);
    });
    
    console.log('\n快速修复完成！');
    console.log('\n现在你有几个选择：');
    console.log('1. 使用浏览器中的PNG生成器（已打开）');
    console.log('2. 使用在线转换工具转换 *-fixed.svg 文件');
    console.log('3. 暂时使用当前的占位PNG文件（功能正常，但图标简单）');
}

// 检查当前PNG文件状态
function checkPNGStatus() {
    console.log('检查当前PNG文件状态...\n');
    
    tabBarConfigs.forEach(config => {
        const pngPath = path.join(__dirname, config.name);
        if (fs.existsSync(pngPath)) {
            const stats = fs.statSync(pngPath);
            const status = stats.size > 100 ? '✓ 正常' : '⚠ 占位文件';
            console.log(`${config.name}: ${stats.size} 字节 - ${status}`);
        } else {
            console.log(`${config.name}: 文件不存在`);
        }
    });
}

// 执行检查和修复
if (require.main === module) {
    try {
        checkPNGStatus();
        console.log('\n' + '='.repeat(50));
        quickFixTabBar();
        
        console.log('\n' + '='.repeat(50));
        console.log('📋 解决方案总结：');
        console.log('1. 当前PNG文件可以让小程序正常启动');
        console.log('2. 已生成更好的SVG文件供转换');
        console.log('3. 浏览器PNG生成器已打开，可直接下载');
        console.log('4. 建议使用方案1或2获得更好的图标效果');
    } catch (error) {
        console.error('处理过程中出错:', error);
    }
}

module.exports = { quickFixTabBar, checkPNGStatus };
