<!--pages/search/search.wxml-->
<view class="search-container">
  <!-- 搜索框区域 -->
  <view class="search-header">
    <view class="search-input-wrapper">
      <view class="search-icon">
        <image src="/images/icon-search.svg" mode="aspectFit"></image>
      </view>
      <input
        id="search-input"
        class="search-input"
        type="text"
        placeholder="搜索试卷、练习册、课件..."
        value="{{keyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchSubmit"
        confirm-type="search"
        focus="{{!hasSearched}}"
      />
      <view class="clear-btn" wx:if="{{keyword}}" bindtap="onClearSearch">
        <image src="/images/icon-close.svg" mode="aspectFit"></image>
      </view>
    </view>
    <view class="search-btn" bindtap="onSearchSubmit">搜索</view>
  </view>

  <!-- 搜索历史和热门搜索 -->
  <view class="search-suggestions" wx:if="{{showHistory}}">
    <!-- 搜索历史 -->
    <view class="history-section" wx:if="{{searchHistory.length > 0}}">
      <view class="section-header">
        <view class="section-title">搜索历史</view>
        <view class="clear-history-btn" bindtap="onClearHistory">
          <image src="/images/icon-close.svg" mode="aspectFit"></image>
          <text>清空</text>
        </view>
      </view>
      <view class="history-list">
        <view
          class="history-item"
          wx:for="{{searchHistory}}"
          wx:key="*this"
          data-keyword="{{item}}"
          bindtap="onSearchSubmit"
        >
          <view class="history-text">{{item}}</view>
          <view
            class="delete-history-btn"
            data-keyword="{{item}}"
            bindtap="onDeleteHistory"
            catchtap=""
          >
            <image src="/images/icon-close.svg" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 热门搜索 -->
    <view class="hot-section">
      <view class="section-title">热门搜索</view>
      <view class="hot-keywords">
        <view
          class="hot-keyword"
          wx:for="{{hotKeywords}}"
          wx:key="*this"
          data-keyword="{{item}}"
          bindtap="onSearchSubmit"
        >
          {{item}}
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{hasSearched}}">
    <!-- 搜索状态提示 -->
    <view class="search-status">
      <text wx:if="{{searching}}">正在搜索"{{keyword}}"...</text>
      <text wx:elif="{{searchResults.length > 0}}">找到 {{searchResults.length}} 个相关结果</text>
      <text wx:else>未找到"{{keyword}}"的相关结果</text>
    </view>

    <!-- 搜索结果列表 -->
    <view class="results-list" wx:if="{{searchResults.length > 0}}">
      <view
        class="result-item"
        wx:for="{{searchResults}}"
        wx:key="_id"
        data-material="{{item}}"
        bindtap="onResultTap"
      >
        <view class="result-cover">
          <image
            src="{{item.cover_image || '/images/default-cover.svg'}}"
            mode="aspectFill"
            lazy-load="true"
          ></image>
        </view>
        <view class="result-info">
          <view class="result-title">{{item.title}}</view>
          <view class="result-desc" wx:if="{{item.description}}">{{item.description}}</view>
          <view class="result-meta">
            <view class="result-tags">
              <text class="tag tag-primary" wx:if="{{item.grade_name}}">{{item.grade_name}}</text>
              <text class="tag tag-secondary" wx:if="{{item.subject_name}}">{{item.subject_name}}</text>
              <text class="tag tag-secondary" wx:if="{{item.textbook_name}}">{{item.textbook_name}}</text>
            </view>
            <view class="result-points">{{item.points_cost}}积分</view>
          </view>
          <view class="result-stats">
            <text>{{item.download_count || 0}}次下载</text>
            <text>{{item.view_count || 0}}次浏览</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && searchResults.length > 0}}">
      <text wx:if="{{loadingMore}}">加载中...</text>
      <text wx:else>上拉加载更多</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-results" wx:if="{{!searching && searchResults.length === 0}}">
      <image src="/images/empty-materials.svg" mode="aspectFit"></image>
      <view class="empty-text">
        <text>未找到相关资料</text>
        <text>试试其他关键词吧</text>
      </view>
    </view>
  </view>

  <!-- 搜索中的加载状态 -->
  <view class="searching-state" wx:if="{{searching && !hasSearched}}">
    <view class="loading-spinner"></view>
    <text>正在搜索...</text>
  </view>
</view>