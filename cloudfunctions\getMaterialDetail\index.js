// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { materialId } = event
  const wxContext = cloud.getWXContext()

  try {
    if (!materialId) {
      return {
        success: false,
        message: '资料ID不能为空'
      }
    }

    // 查询资料详情
    const materialResult = await db.collection('materials')
      .doc(materialId)
      .get()

    if (!materialResult.data) {
      return {
        success: false,
        message: '资料不存在'
      }
    }

    const material = materialResult.data

    // 检查资料是否有效
    if (!material.is_active) {
      return {
        success: false,
        message: '资料已下架'
      }
    }

    // 获取分类信息
    const categoriesResult = await db.collection('categories').get()
    const categories = categoriesResult.data

    // 创建分类映射
    const categoryMap = {}
    categories.forEach(category => {
      if (category.options) {
        category.options.forEach(option => {
          categoryMap[option.id] = {
            name: option.name,
            categoryName: category.name
          }
        })
      }
    })

    // 为资料添加分类名称
    const categoryNames = {}
    if (material.category_ids && material.category_ids.length > 0) {
      material.category_ids.forEach(categoryId => {
        const categoryInfo = categoryMap[categoryId]
        if (categoryInfo) {
          // 根据分类类型设置对应的名称
          if (categoryId.startsWith('grade_')) {
            categoryNames.grade_name = categoryInfo.name
          } else if (['chinese', 'math', 'english', 'science', 'morality'].includes(categoryId)) {
            categoryNames.subject_name = categoryInfo.name
          } else if (categoryId.includes('semester')) {
            categoryNames.semester_name = categoryInfo.name
          } else if (['renjiao', 'beijing', 'sujiao', 'jiangsu', 'shanghai'].includes(categoryId)) {
            categoryNames.textbook_name = categoryInfo.name
          } else if (['exam', 'exercise', 'courseware', 'material'].includes(categoryId)) {
            categoryNames.project_type_name = categoryInfo.name
          }
        }
      })
    }

    // 格式化上传时间
    let upload_time_formatted = ''
    if (material.upload_time) {
      const date = new Date(material.upload_time)
      upload_time_formatted = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }

    // 增加浏览次数
    try {
      await db.collection('materials')
        .doc(materialId)
        .update({
          data: {
            view_count: db.command.inc(1)
          }
        })
    } catch (error) {
      console.error('更新浏览次数失败:', error)
    }

    // 返回完整的资料信息
    const enrichedMaterial = {
      ...material,
      ...categoryNames,
      upload_time_formatted,
      view_count: (material.view_count || 0) + 1
    }

    return {
      success: true,
      data: enrichedMaterial
    }

  } catch (error) {
    console.error('获取资料详情失败:', error)
    return {
      success: false,
      message: '获取资料详情失败'
    }
  }
}
