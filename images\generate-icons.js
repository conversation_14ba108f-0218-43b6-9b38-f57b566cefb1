const fs = require('fs');
const path = require('path');

// 图标配置
const iconConfigs = {
    tab: [
        { name: 'tab-home.png', size: 81, color: '#999999', symbol: '🏠', type: 'emoji' },
        { name: 'tab-home-active.png', size: 81, color: '#FF6B35', symbol: '🏠', type: 'emoji' },
        { name: 'tab-category.png', size: 81, color: '#999999', symbol: '📋', type: 'emoji' },
        { name: 'tab-category-active.png', size: 81, color: '#FF6B35', symbol: '📋', type: 'emoji' },
        { name: 'tab-profile.png', size: 81, color: '#999999', symbol: '👤', type: 'emoji' },
        { name: 'tab-profile-active.png', size: 81, color: '#FF6B35', symbol: '👤', type: 'emoji' }
    ],
    function: [
        { name: 'icon-search.png', size: 48, color: '#666666', symbol: '🔍', type: 'emoji' },
        { name: 'icon-arrow-right.png', size: 48, color: '#666666', symbol: '▶', type: 'text' },
        { name: 'icon-arrow-down.png', size: 48, color: '#666666', symbol: '▼', type: 'text' },
        { name: 'icon-star.png', size: 48, color: '#666666', symbol: '☆', type: 'text' },
        { name: 'icon-star-filled.png', size: 48, color: '#FF6B35', symbol: '★', type: 'text' },
        { name: 'icon-share.png', size: 48, color: '#666666', symbol: '📤', type: 'emoji' },
        { name: 'icon-download.png', size: 48, color: '#666666', symbol: '📥', type: 'emoji' },
        { name: 'icon-points.png', size: 48, color: '#FF6B35', symbol: '💰', type: 'emoji' },
        { name: 'icon-close.png', size: 48, color: '#666666', symbol: '✕', type: 'text' },
        { name: 'icon-check.png', size: 48, color: '#4CAF50', symbol: '✓', type: 'text' },
        { name: 'icon-earn.png', size: 48, color: '#FF6B35', symbol: '💎', type: 'emoji' },
        { name: 'icon-service.png', size: 48, color: '#666666', symbol: '🎧', type: 'emoji' },
        { name: 'icon-help.png', size: 48, color: '#666666', symbol: '❓', type: 'emoji' }
    ],
    grade: [
        { name: 'grade-grade_1.png', size: 120, color: '#FF6B35', symbol: '1', type: 'number' },
        { name: 'grade-grade_2.png', size: 120, color: '#FF6B35', symbol: '2', type: 'number' },
        { name: 'grade-grade_3.png', size: 120, color: '#FF6B35', symbol: '3', type: 'number' },
        { name: 'grade-grade_4.png', size: 120, color: '#FF6B35', symbol: '4', type: 'number' },
        { name: 'grade-grade_5.png', size: 120, color: '#FF6B35', symbol: '5', type: 'number' },
        { name: 'grade-grade_6.png', size: 120, color: '#FF6B35', symbol: '6', type: 'number' }
    ]
};

// 占位图片配置
const placeholderConfigs = [
    { name: 'default-cover.png', width: 400, height: 300, text: '默认封面', bgColor: '#f0f0f0', textColor: '#999999' },
    { name: 'default-avatar.png', width: 200, height: 200, text: '头像', bgColor: '#e0e0e0', textColor: '#bbb', isCircle: true },
    { name: 'empty-materials.png', width: 400, height: 400, text: '暂无资料', emoji: '📚', bgColor: 'transparent' },
    { name: 'empty-favorites.png', width: 400, height: 400, text: '暂无收藏', emoji: '⭐', bgColor: 'transparent' },
    { name: 'empty-downloads.png', width: 400, height: 400, text: '暂无下载', emoji: '📥', bgColor: 'transparent' },
    { name: 'share-cover.jpg', width: 1200, height: 630, text: '小学教辅资料', subtitle: '海量优质学习资源', bgColor: '#FF6B35', textColor: '#ffffff', isShareCover: true }
];

// 生成SVG图标
function generateSVGIcon(config) {
    let content = '';
    
    if (config.type === 'emoji') {
        content = `
            <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" 
                  font-size="${config.size * 0.6}" font-family="Arial">${config.symbol}</text>
        `;
    } else if (config.type === 'number') {
        content = `
            <circle cx="50%" cy="50%" r="${config.size * 0.4}" fill="rgba(255, 107, 53, 0.1)" stroke="${config.color}" stroke-width="2"/>
            <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" 
                  font-size="${config.size * 0.4}" font-family="Arial" font-weight="bold" fill="${config.color}">${config.symbol}</text>
        `;
    } else {
        content = `
            <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" 
                  font-size="${config.size * 0.5}" font-family="Arial" fill="${config.color}">${config.symbol}</text>
        `;
    }

    return `
        <svg width="${config.size}" height="${config.size}" xmlns="http://www.w3.org/2000/svg">
            ${content}
        </svg>
    `;
}

// 生成占位图片SVG
function generatePlaceholderSVG(config) {
    if (config.isShareCover) {
        return `
            <svg width="${config.width}" height="${config.height}" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#F7931E;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#grad)"/>
                <text x="50%" y="45%" text-anchor="middle" dominant-baseline="middle" 
                      font-size="80" font-family="Arial" font-weight="bold" fill="${config.textColor}">${config.text}</text>
                <text x="50%" y="60%" text-anchor="middle" dominant-baseline="middle" 
                      font-size="40" font-family="Arial" fill="${config.textColor}">${config.subtitle}</text>
            </svg>
        `;
    } else if (config.isCircle) {
        return `
            <svg width="${config.width}" height="${config.height}" xmlns="http://www.w3.org/2000/svg">
                <circle cx="50%" cy="50%" r="${config.width * 0.5}" fill="${config.bgColor}"/>
                <circle cx="50%" cy="35%" r="${config.width * 0.15}" fill="${config.textColor}"/>
                <path d="M ${config.width * 0.25} ${config.height * 0.8} A ${config.width * 0.25} ${config.width * 0.25} 0 0 1 ${config.width * 0.75} ${config.height * 0.8}" fill="${config.textColor}"/>
            </svg>
        `;
    } else if (config.emoji) {
        return `
            <svg width="${config.width}" height="${config.height}" xmlns="http://www.w3.org/2000/svg">
                <circle cx="50%" cy="50%" r="${config.width * 0.4}" fill="rgba(255, 107, 53, 0.1)"/>
                <text x="50%" y="40%" text-anchor="middle" dominant-baseline="middle" 
                      font-size="${config.width * 0.3}" font-family="Arial">${config.emoji}</text>
                <text x="50%" y="70%" text-anchor="middle" dominant-baseline="middle" 
                      font-size="${config.width * 0.08}" font-family="Arial" fill="#999999">${config.text}</text>
            </svg>
        `;
    } else {
        return `
            <svg width="${config.width}" height="${config.height}" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="${config.bgColor}"/>
                <text x="50%" y="45%" text-anchor="middle" dominant-baseline="middle" 
                      font-size="${Math.min(config.width, config.height) / 10}" font-family="Arial" fill="${config.textColor}">${config.text}</text>
                <text x="50%" y="60%" text-anchor="middle" dominant-baseline="middle" 
                      font-size="${Math.min(config.width, config.height) / 15}" font-family="Arial" fill="${config.textColor}">${config.width} × ${config.height}</text>
            </svg>
        `;
    }
}

// 创建图标文件
function createIconFiles() {
    console.log('开始生成图标文件...\n');

    // 生成图标
    Object.keys(iconConfigs).forEach(category => {
        console.log(`生成 ${category} 图标:`);
        iconConfigs[category].forEach(config => {
            const svg = generateSVGIcon(config);
            const filePath = path.join(__dirname, config.name);
            
            // 将SVG内容写入文件（作为参考）
            const svgPath = filePath.replace('.png', '.svg');
            fs.writeFileSync(svgPath, svg);
            
            console.log(`  ✓ ${config.name} (${config.size}x${config.size})`);
        });
        console.log('');
    });

    // 生成占位图片
    console.log('生成占位图片:');
    placeholderConfigs.forEach(config => {
        const svg = generatePlaceholderSVG(config);
        const extension = config.name.endsWith('.jpg') ? '.svg' : '.svg';
        const filePath = path.join(__dirname, config.name.replace(/\.(png|jpg)$/, extension));
        
        fs.writeFileSync(filePath, svg);
        console.log(`  ✓ ${config.name} (${config.width}x${config.height})`);
    });

    console.log('\n图标生成完成！');
    console.log('\n注意：');
    console.log('1. 生成的是SVG格式文件，可以在浏览器中查看');
    console.log('2. 如需PNG格式，请使用在线转换工具或安装canvas库');
    console.log('3. SVG文件可以直接在小程序中使用，支持缩放不失真');
}

// 生成使用说明
function generateUsageInstructions() {
    const instructions = `
# 图标使用说明

## 已生成的文件

### Tab Bar 图标 (81x81px)
- tab-home.svg / tab-home-active.svg - 首页图标
- tab-category.svg / tab-category-active.svg - 分类图标  
- tab-profile.svg / tab-profile-active.svg - 个人中心图标

### 功能图标 (48x48px)
- icon-search.svg - 搜索图标
- icon-arrow-right.svg / icon-arrow-down.svg - 箭头图标
- icon-star.svg / icon-star-filled.svg - 收藏图标
- icon-share.svg - 分享图标
- icon-download.svg - 下载图标
- icon-points.svg - 积分图标
- icon-close.svg - 关闭图标
- icon-check.svg - 确认图标
- icon-earn.svg - 赚积分图标
- icon-service.svg - 客服图标
- icon-help.svg - 帮助图标

### 年级图标 (120x120px)
- grade-grade_1.svg ~ grade-grade_6.svg - 一到六年级图标

### 占位图片
- default-cover.svg - 默认封面 (400x300px)
- default-avatar.svg - 默认头像 (200x200px)
- empty-materials.svg - 空状态插图 (400x400px)
- empty-favorites.svg - 收藏为空插图 (400x400px)
- empty-downloads.svg - 下载历史为空插图 (400x400px)
- share-cover.svg - 分享封面 (1200x630px)

## 在小程序中使用SVG

小程序支持SVG格式的图片，可以直接使用：

\`\`\`xml
<image src="/images/icon-search.svg" mode="aspectFit"></image>
\`\`\`

## 转换为PNG格式

如果需要PNG格式，可以：
1. 使用在线转换工具：https://convertio.co/svg-png/
2. 使用Photoshop或其他图像编辑软件打开SVG并导出PNG
3. 安装Node.js的canvas库进行批量转换

## 自定义图标

如需修改图标，可以：
1. 编辑对应的SVG文件
2. 修改 generate-icons.js 中的配置重新生成
3. 使用专业的图标设计工具
`;

    fs.writeFileSync(path.join(__dirname, 'USAGE.md'), instructions);
    console.log('使用说明已生成: USAGE.md');
}

// 执行生成
if (require.main === module) {
    try {
        createIconFiles();
        generateUsageInstructions();
    } catch (error) {
        console.error('生成图标时出错:', error);
    }
}

module.exports = { createIconFiles, generateUsageInstructions };
