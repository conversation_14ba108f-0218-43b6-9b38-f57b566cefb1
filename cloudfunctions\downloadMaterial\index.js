// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { materialId } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    if (!materialId) {
      return {
        success: false,
        message: '资料ID不能为空'
      }
    }

    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }

    // 查询资料信息
    const materialResult = await db.collection('materials')
      .doc(materialId)
      .get()

    if (!materialResult.data) {
      return {
        success: false,
        message: '资料不存在'
      }
    }

    const material = materialResult.data

    // 检查资料是否有效
    if (!material.is_active) {
      return {
        success: false,
        message: '资料已下架'
      }
    }

    // 查询用户信息
    const userResult = await db.collection('users')
      .where({
        openid: openid
      })
      .get()

    if (!userResult.data || userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userResult.data[0]

    // 检查是否已经下载过
    const downloadResult = await db.collection('user_downloads')
      .where({
        user_id: user._id,
        material_id: materialId
      })
      .get()

    const isAlreadyDownloaded = downloadResult.data && downloadResult.data.length > 0

    let newBalance = user.points

    // 如果是新下载，需要扣除积分
    if (!isAlreadyDownloaded) {
      // 检查积分是否足够
      if (user.points < material.points_cost) {
        return {
          success: false,
          message: `积分不足，需要 ${material.points_cost} 积分`
        }
      }

      // 扣除积分
      newBalance = user.points - material.points_cost

      // 更新用户积分
      await db.collection('users')
        .doc(user._id)
        .update({
          data: {
            points: newBalance,
            update_time: new Date()
          }
        })

      // 记录积分流水
      await db.collection('points_log')
        .add({
          data: {
            user_id: user._id,
            material_id: materialId,
            points_change: -material.points_cost,
            points_balance: newBalance,
            change_type: 'download',
            description: `下载资料：${material.title}`,
            create_time: new Date()
          }
        })

      // 记录下载历史
      await db.collection('user_downloads')
        .add({
          data: {
            user_id: user._id,
            material_id: materialId,
            download_time: new Date(),
            points_cost: material.points_cost
          }
        })

      // 增加资料下载次数
      await db.collection('materials')
        .doc(materialId)
        .update({
          data: {
            download_count: db.command.inc(1)
          }
        })
    }

    // 生成下载链接
    let downloadUrl = ''
    if (material.file_url) {
      try {
        // 获取文件下载链接
        const fileResult = await cloud.getTempFileURL({
          fileList: [material.file_url]
        })
        
        if (fileResult.fileList && fileResult.fileList.length > 0) {
          downloadUrl = fileResult.fileList[0].tempFileURL
        }
      } catch (error) {
        console.error('获取文件下载链接失败:', error)
        // 如果获取临时链接失败，使用原始链接
        downloadUrl = material.file_url
      }
    }

    return {
      success: true,
      data: {
        downloadUrl,
        newBalance,
        isNewDownload: !isAlreadyDownloaded,
        pointsCost: isAlreadyDownloaded ? 0 : material.points_cost
      }
    }

  } catch (error) {
    console.error('下载资料失败:', error)
    return {
      success: false,
      message: '下载失败，请稍后重试'
    }
  }
}
