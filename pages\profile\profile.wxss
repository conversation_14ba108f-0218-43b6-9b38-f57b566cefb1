/* pages/profile/profile.wxss */
.profile-container {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 用户信息卡 */
.user-card {
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  margin-bottom: 20rpx;
  padding: 40rpx 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
  color: #fff;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 15rpx;
}

.user-points {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  opacity: 0.9;
}

.user-points image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

/* 未登录状态 */
.login-card {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 60rpx 30rpx;
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.login-avatar image {
  width: 100%;
  height: 100%;
}

.login-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.login-btn {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 积分服务区 */
.points-section {
  background: #fff;
  margin-bottom: 20rpx;
  display: flex;
}

.points-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  position: relative;
}

.points-item:active {
  background: #f8f8f8;
}

.points-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 15rpx;
}

.points-icon image {
  width: 100%;
  height: 100%;
}

.points-text {
  font-size: 26rpx;
  color: #333;
}

.earn-points .points-text {
  color: #FF6B35;
  font-weight: 600;
}

.points-divider {
  width: 1rpx;
  height: 60rpx;
  background: #f0f0f0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.points-arrow {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 24rpx;
  height: 24rpx;
}

.points-arrow image {
  width: 100%;
  height: 100%;
}

/* 功能列表 */
.function-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item:active {
  background: #f8f8f8;
}

.function-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 30rpx;
}

.function-icon image {
  width: 100%;
  height: 100%;
}

.function-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.function-badge {
  font-size: 22rpx;
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}

.function-arrow {
  width: 24rpx;
  height: 24rpx;
}

.function-arrow image {
  width: 100%;
  height: 100%;
}

.share-item {
  background: linear-gradient(90deg, rgba(255, 107, 53, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
}