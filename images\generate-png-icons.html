<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNG图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-preview {
            width: 81px;
            height: 81px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .icon-name {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #E55A2B;
        }
        .instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
        .batch-download {
            text-align: center;
            margin: 20px 0;
        }
        .batch-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        .batch-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TabBar PNG图标生成器</h1>
        
        <div class="instructions">
            <h3>⚠️ 重要说明：</h3>
            <p>微信小程序的TabBar不支持SVG格式，只支持PNG和JPG格式。这个工具可以直接生成PNG格式的TabBar图标。</p>
            <p><strong>使用方法：</strong>点击"下载"按钮生成单个图标，或点击"批量下载"一次性生成所有图标。</p>
        </div>

        <div class="batch-download">
            <button class="batch-btn" onclick="downloadAllIcons()">批量下载所有TabBar图标</button>
            <button class="batch-btn" onclick="downloadAllFunctionIcons()">批量下载所有功能图标</button>
        </div>

        <h2>TabBar图标 (81x81px)</h2>
        <div class="icon-grid" id="tabbar-icons"></div>

        <h2>功能图标 (48x48px)</h2>
        <div class="icon-grid" id="function-icons"></div>
    </div>

    <script>
        // TabBar图标配置
        const tabbarIcons = [
            { name: 'tab-home.png', size: 81, color: '#999999', text: 'H', desc: '首页(未选中)' },
            { name: 'tab-home-active.png', size: 81, color: '#FF6B35', text: 'H', desc: '首页(选中)' },
            { name: 'tab-category.png', size: 81, color: '#999999', text: 'C', desc: '分类(未选中)' },
            { name: 'tab-category-active.png', size: 81, color: '#FF6B35', text: 'C', desc: '分类(选中)' },
            { name: 'tab-profile.png', size: 81, color: '#999999', text: 'M', desc: '我的(未选中)' },
            { name: 'tab-profile-active.png', size: 81, color: '#FF6B35', text: 'M', desc: '我的(选中)' }
        ];

        // 功能图标配置
        const functionIcons = [
            { name: 'icon-search.png', size: 48, color: '#666666', text: '🔍', desc: '搜索' },
            { name: 'icon-arrow-right.png', size: 48, color: '#666666', text: '▶', desc: '右箭头' },
            { name: 'icon-arrow-down.png', size: 48, color: '#666666', text: '▼', desc: '下箭头' },
            { name: 'icon-star.png', size: 48, color: '#666666', text: '☆', desc: '收藏' },
            { name: 'icon-star-filled.png', size: 48, color: '#FF6B35', text: '★', desc: '已收藏' },
            { name: 'icon-share.png', size: 48, color: '#666666', text: '📤', desc: '分享' },
            { name: 'icon-download.png', size: 48, color: '#666666', text: '📥', desc: '下载' },
            { name: 'icon-points.png', size: 48, color: '#FF6B35', text: '💰', desc: '积分' },
            { name: 'icon-close.png', size: 48, color: '#666666', text: '✕', desc: '关闭' },
            { name: 'icon-check.png', size: 48, color: '#4CAF50', text: '✓', desc: '确认' }
        ];

        // 生成PNG图标
        function generatePNGIcon(config) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = config.size;
            canvas.height = config.size;

            // 透明背景
            ctx.clearRect(0, 0, config.size, config.size);

            // 绘制背景圆圈（TabBar图标）
            if (config.size === 81) {
                ctx.fillStyle = config.color;
                ctx.globalAlpha = 0.1;
                ctx.beginPath();
                ctx.arc(config.size / 2, config.size / 2, config.size * 0.35, 0, 2 * Math.PI);
                ctx.fill();
                ctx.globalAlpha = 1;
            }

            // 绘制文字或符号
            ctx.fillStyle = config.color;
            ctx.font = `bold ${config.size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(config.text, config.size / 2, config.size / 2);

            return canvas;
        }

        // 下载单个图标
        function downloadIcon(config) {
            const canvas = generatePNGIcon(config);
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = config.name;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }

        // 批量下载TabBar图标
        function downloadAllIcons() {
            tabbarIcons.forEach((config, index) => {
                setTimeout(() => {
                    downloadIcon(config);
                }, index * 500); // 延迟下载，避免浏览器阻止
            });
        }

        // 批量下载功能图标
        function downloadAllFunctionIcons() {
            functionIcons.forEach((config, index) => {
                setTimeout(() => {
                    downloadIcon(config);
                }, index * 500);
            });
        }

        // 生成图标网格
        function generateIconGrid(containerId, icons) {
            const container = document.getElementById(containerId);
            
            icons.forEach(config => {
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                
                // 创建预览canvas
                const canvas = generatePNGIcon(config);
                canvas.style.width = '60px';
                canvas.style.height = '60px';
                preview.appendChild(canvas);
                
                const name = document.createElement('div');
                name.className = 'icon-name';
                name.textContent = `${config.name} - ${config.desc}`;
                
                const btn = document.createElement('button');
                btn.className = 'download-btn';
                btn.textContent = '下载PNG';
                btn.onclick = () => downloadIcon(config);
                
                item.appendChild(preview);
                item.appendChild(name);
                item.appendChild(btn);
                container.appendChild(item);
            });
        }

        // 页面加载完成后生成图标
        document.addEventListener('DOMContentLoaded', function() {
            generateIconGrid('tabbar-icons', tabbarIcons);
            generateIconGrid('function-icons', functionIcons);
        });
    </script>
</body>
</html>
