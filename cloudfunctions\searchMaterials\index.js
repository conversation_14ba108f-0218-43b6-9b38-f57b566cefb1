// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { keyword, page = 1, limit = 20 } = event

  try {
    if (!keyword || !keyword.trim()) {
      return {
        success: false,
        message: '搜索关键词不能为空'
      }
    }

    const trimmedKeyword = keyword.trim()
    const skip = (page - 1) * limit

    // 构建搜索条件
    const searchConditions = _.or([
      {
        title: db.RegExp({
          regexp: trimmedKeyword,
          options: 'i'
        })
      },
      {
        description: db.RegExp({
          regexp: trimmedKeyword,
          options: 'i'
        })
      },
      {
        tags: db.RegExp({
          regexp: trimmedKeyword,
          options: 'i'
        })
      }
    ])

    // 查询总数
    const countResult = await db.collection('materials')
      .where({
        is_active: true,
        ...searchConditions
      })
      .count()

    const total = countResult.total

    // 查询资料列表
    const materialsResult = await db.collection('materials')
      .where({
        is_active: true,
        ...searchConditions
      })
      .orderBy('sort_order', 'asc')
      .orderBy('upload_time', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    const materials = materialsResult.data

    // 获取分类信息，用于显示标签
    const categoriesResult = await db.collection('categories').get()
    const categories = categoriesResult.data

    // 创建分类映射
    const categoryMap = {}
    categories.forEach(category => {
      if (category.options) {
        category.options.forEach(option => {
          categoryMap[option.id] = {
            name: option.name,
            categoryName: category.name
          }
        })
      }
    })

    // 为每个资料添加分类名称
    const enrichedMaterials = materials.map(material => {
      const categoryNames = {}
      
      if (material.category_ids && material.category_ids.length > 0) {
        material.category_ids.forEach(categoryId => {
          const categoryInfo = categoryMap[categoryId]
          if (categoryInfo) {
            // 根据分类类型设置对应的名称
            if (categoryId.startsWith('grade_')) {
              categoryNames.grade_name = categoryInfo.name
            } else if (['chinese', 'math', 'english', 'science', 'morality'].includes(categoryId)) {
              categoryNames.subject_name = categoryInfo.name
            } else if (categoryId.includes('semester')) {
              categoryNames.semester_name = categoryInfo.name
            } else if (['renjiao', 'beijing', 'sujiao', 'jiangsu', 'shanghai'].includes(categoryId)) {
              categoryNames.textbook_name = categoryInfo.name
            }
          }
        })
      }

      return {
        ...material,
        ...categoryNames
      }
    })

    // 计算是否还有更多数据
    const hasMore = skip + materials.length < total

    return {
      success: true,
      data: {
        materials: enrichedMaterials,
        total,
        page,
        limit,
        hasMore,
        keyword: trimmedKeyword
      }
    }
  } catch (error) {
    console.error('搜索资料失败:', error)
    return {
      success: false,
      message: '搜索失败，请稍后重试'
    }
  }
}
