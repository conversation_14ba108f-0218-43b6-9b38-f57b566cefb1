<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 用户信息卡 -->
  <view class="user-card" wx:if="{{isLogin}}">
    <view class="user-info" bindtap="onGetUserProfile">
      <view class="user-avatar">
        <image
          src="{{userInfo.avatarUrl || '/images/default-avatar.svg'}}"
          mode="aspectFill"
        ></image>
      </view>
      <view class="user-details">
        <view class="user-name">{{userInfo.nickName || '点击设置昵称'}}</view>
        <view class="user-points">
          <image src="/images/icon-points.svg" mode="aspectFit"></image>
          <text>我的积分：{{userInfo.points || 0}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 未登录状态 -->
  <view class="login-card" wx:if="{{!isLogin}}">
    <view class="login-content">
      <view class="login-avatar">
        <image src="/images/default-avatar.svg" mode="aspectFill"></image>
      </view>
      <view class="login-text">点击登录使用更多功能</view>
      <button class="login-btn btn btn-primary" bindtap="onLogin">立即登录</button>
    </view>
  </view>

  <!-- 积分服务区 -->
  <view class="points-section" wx:if="{{isLogin}}">
    <view class="points-item" bindtap="onPointsDetail">
      <view class="points-icon">
        <image src="/images/icon-points.svg" mode="aspectFit"></image>
      </view>
      <view class="points-text">积分明细</view>
      <view class="points-arrow">
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>
    <view class="points-divider"></view>
    <view class="points-item earn-points" bindtap="onEarnPoints">
      <view class="points-icon">
        <image src="/images/icon-earn.svg" mode="aspectFit"></image>
      </view>
      <view class="points-text">去赚积分</view>
      <view class="points-arrow">
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="function-section">
    <view class="function-item" bindtap="onMyDownloads">
      <view class="function-icon">
        <image src="/images/icon-download.svg" mode="aspectFit"></image>
      </view>
      <view class="function-text">我的下载</view>
      <view class="function-arrow">
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>

    <view class="function-item" bindtap="onMyFavorites">
      <view class="function-icon">
        <image src="/images/icon-star.svg" mode="aspectFit"></image>
      </view>
      <view class="function-text">我的收藏</view>
      <view class="function-arrow">
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>

    <view class="function-item share-item" bindtap="onShareApp">
      <view class="function-icon">
        <image src="/images/icon-share.svg" mode="aspectFit"></image>
      </view>
      <view class="function-text">分享小程序</view>
      <view class="function-badge">邀请好友，同获积分奖励</view>
      <view class="function-arrow">
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>

    <view class="function-item" bindtap="onContactService">
      <view class="function-icon">
        <image src="/images/icon-service.svg" mode="aspectFit"></image>
      </view>
      <view class="function-text">联系客服</view>
      <view class="function-arrow">
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>

    <view class="function-item" bindtap="onHelpFeedback">
      <view class="function-icon">
        <image src="/images/icon-help.svg" mode="aspectFit"></image>
      </view>
      <view class="function-text">帮助与反馈</view>
      <view class="function-arrow">
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>