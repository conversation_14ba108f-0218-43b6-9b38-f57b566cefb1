<!--pages/category/category.wxml-->
<view class="category-container">
  <!-- 筛选器区域 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="filter-list">
        <view
          class="filter-item {{selectedFilters[item._id] ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="_id"
          data-type="{{item._id}}"
          bindtap="onFilterTap"
        >
          <text>{{selectedFilters[item._id] ? selectedFilters[item._id].name : item.name}}</text>
          <image src="/images/icon-arrow-down.svg" mode="aspectFit"></image>
        </view>
        <view class="filter-item reset-btn" bindtap="onResetFilters">
          <text>重置</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 快速分类导航 -->
  <view class="quick-nav-section" wx:if="{{!loading}}">
    <view
      class="category-group"
      wx:for="{{categories}}"
      wx:key="_id"
      wx:if="{{item.options && item.options.length > 0}}"
    >
      <view class="category-title">{{item.name}}</view>
      <view class="category-options">
        <view
          class="option-item"
          wx:for="{{item.options}}"
          wx:for-item="option"
          wx:key="id"
          wx:if="{{option.is_active}}"
          data-category="{{item._id}}"
          data-option="{{option}}"
          bindtap="onQuickCategory"
        >
          <view class="option-name">{{option.name}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 应用筛选按钮 -->
  <view class="apply-section" wx:if="{{Object.keys(selectedFilters).length > 0}}">
    <button class="apply-btn btn btn-primary" bindtap="onApplyFilters">
      查看筛选结果
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && categories.length === 0}}">
    <image src="/images/empty-materials.svg" mode="aspectFit"></image>
    <text>暂无分类数据</text>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-modal" wx:if="{{filterVisible}}" bindtap="onFilterClose">
  <view class="filter-content" catchtap="">
    <view class="filter-header">
      <text>选择{{currentFilterCategory.name}}</text>
      <view class="close-btn" bindtap="onFilterClose">
        <image src="/images/icon-close.svg" mode="aspectFit"></image>
      </view>
    </view>
    <view class="filter-options">
      <view
        class="filter-option {{selectedFilters[currentFilterType] && selectedFilters[currentFilterType].id === option.id ? 'selected' : ''}}"
        wx:for="{{currentFilterOptions}}"
        wx:for-item="option"
        wx:key="id"
        wx:if="{{option.is_active}}"
        data-option="{{option}}"
        bindtap="onFilterOptionTap"
      >
        <text>{{option.name}}</text>
        <image
          src="/images/icon-check.svg"
          mode="aspectFit"
          wx:if="{{selectedFilters[currentFilterType] && selectedFilters[currentFilterType].id === option.id}}"
        ></image>
      </view>
    </view>
  </view>
</view>