/* pages/home/<USER>/
.home-container {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  height: 80rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.search-icon image {
  width: 100%;
  height: 100%;
}

.search-placeholder {
  flex: 1;
  color: #999;
  font-size: 28rpx;
}

/* 年级分类区域 */
.grade-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.grade-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.grade-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #fff5f2 0%, #fff 100%);
  border-radius: 20rpx;
  border: 2rpx solid #ffe8e0;
  transition: all 0.3s ease;
}

.grade-item:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #ffede6 0%, #fff5f2 100%);
}

.grade-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
}

.grade-icon image {
  width: 100%;
  height: 100%;
}

.grade-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 推荐区域 */
.recommend-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.more-btn {
  display: flex;
  align-items: center;
  color: #FF6B35;
  font-size: 26rpx;
}

.more-btn image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

.recommend-scroll {
  width: 100%;
  white-space: nowrap;
}

.recommend-list {
  display: inline-flex;
  gap: 20rpx;
}

.recommend-item {
  display: inline-block;
  width: 280rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.recommend-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.material-cover {
  width: 100%;
  height: 180rpx;
  background: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.material-cover image {
  width: 100%;
  height: 100%;
}

.material-info {
  padding: 20rpx;
}

.material-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.material-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.material-tags {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.material-points {
  color: #FF6B35;
  font-size: 24rpx;
  font-weight: 600;
  white-space: nowrap;
  margin-left: 10rpx;
}

/* 页面加载状态 */
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
