# 图片资源说明

## 快速生成图标

我已经为你创建了两个HTML工具来快速生成所需的图标：

### 1. 图标生成器 (`generate-icons.html`)
- 打开 `images/generate-icons.html` 文件
- 点击对应图标的"下载"按钮即可生成PNG图片
- 自动生成正确尺寸的图标文件

### 2. 占位图片生成器 (`create-placeholder.html`)
- 打开 `images/create-placeholder.html` 文件
- 生成默认封面、头像、空状态插图等占位图片

## 使用步骤

1. **生成图标**
   ```bash
   # 在浏览器中打开
   images/generate-icons.html

   # 点击下载按钮生成以下图标：
   # - Tab Bar图标 (81x81px)
   # - 功能图标 (48x48px)
   # - 年级图标 (120x120px)
   ```

2. **生成占位图片**
   ```bash
   # 在浏览器中打开
   images/create-placeholder.html

   # 生成以下占位图片：
   # - default-cover.png (400x300px)
   # - default-avatar.png (200x200px)
   # - empty-*.png (400x400px)
   # - share-cover.jpg (1200x630px)
   ```

3. **将生成的图片放入images目录**

## 需要的图片资源清单

### Tab Bar 图标 (81x81px)
- ✅ `tab-home.png` - 首页图标（未选中）
- ✅ `tab-home-active.png` - 首页图标（选中）
- ✅ `tab-category.png` - 分类图标（未选中）
- ✅ `tab-category-active.png` - 分类图标（选中）
- ✅ `tab-profile.png` - 我的图标（未选中）
- ✅ `tab-profile-active.png` - 我的图标（选中）

### 功能图标 (48x48px)
- ✅ `icon-search.png` - 搜索图标
- ✅ `icon-arrow-right.png` - 右箭头图标
- ✅ `icon-arrow-down.png` - 下箭头图标
- ✅ `icon-star.png` - 收藏图标（未收藏）
- ✅ `icon-star-filled.png` - 收藏图标（已收藏）
- ✅ `icon-share.png` - 分享图标
- ✅ `icon-download.png` - 下载图标
- ✅ `icon-points.png` - 积分图标
- ✅ `icon-close.png` - 关闭图标
- ✅ `icon-check.png` - 对勾图标
- ✅ `icon-earn.png` - 赚积分图标
- ✅ `icon-service.png` - 客服图标
- ✅ `icon-help.png` - 帮助图标

### 年级图标 (120x120px)
- ✅ `grade-grade_1.png` - 一年级图标
- ✅ `grade-grade_2.png` - 二年级图标
- ✅ `grade-grade_3.png` - 三年级图标
- ✅ `grade-grade_4.png` - 四年级图标
- ✅ `grade-grade_5.png` - 五年级图标
- ✅ `grade-grade_6.png` - 六年级图标

### 默认图片
- ✅ `default-cover.png` - 默认资料封面 (400x300px)
- ✅ `default-avatar.png` - 默认用户头像 (200x200px)
- ✅ `empty-materials.png` - 空状态插图 (400x400px)
- ✅ `empty-favorites.png` - 收藏为空插图 (400x400px)
- ✅ `empty-downloads.png` - 下载历史为空插图 (400x400px)

### 分享图片
- ✅ `share-cover.jpg` - 分享封面图 (1200x630px)

## 配色方案

### 主色调
- 主色：`#FF6B35` (橙色)
- 辅助色：`#F7931E` (金黄色)
- 文字色：`#333333` (深灰)
- 次要文字：`#999999` (浅灰)

### 图标颜色
- 未选中状态：`#999999`
- 选中状态：`#FF6B35`
- 功能图标：`#666666`

## 图片优化建议

1. **压缩优化**：使用TinyPNG等工具压缩图片
2. **格式选择**：图标使用PNG，照片使用JPG
3. **尺寸适配**：确保在不同设备上显示清晰
4. **命名规范**：使用统一的命名规范便于管理

## 备用方案

如果需要更专业的图标，可以考虑：
- 阿里巴巴矢量图标库 (iconfont.cn)
- Feather Icons (feathericons.com)
- Heroicons (heroicons.com)
- 腾讯TDesign图标库
