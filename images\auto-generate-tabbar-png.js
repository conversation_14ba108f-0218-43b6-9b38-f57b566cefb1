const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

// PNG文件生成器
class PNGGenerator {
    constructor(width, height) {
        this.width = width;
        this.height = height;
        this.data = Buffer.alloc(width * height * 4); // RGBA
    }

    // 设置像素颜色
    setPixel(x, y, r, g, b, a = 255) {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height) return;
        const index = (y * this.width + x) * 4;
        this.data[index] = r;
        this.data[index + 1] = g;
        this.data[index + 2] = b;
        this.data[index + 3] = a;
    }

    // 填充背景色
    fill(r, g, b, a = 0) {
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                this.setPixel(x, y, r, g, b, a);
            }
        }
    }

    // 绘制圆形
    drawCircle(centerX, centerY, radius, r, g, b, a = 255) {
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const dx = x - centerX;
                const dy = y - centerY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                if (distance <= radius) {
                    this.setPixel(x, y, r, g, b, a);
                }
            }
        }
    }

    // 绘制简单的字母（使用像素点阵）
    drawLetter(letter, centerX, centerY, size, r, g, b, a = 255) {
        const patterns = {
            'H': [
                '█   █',
                '█   █',
                '█████',
                '█   █',
                '█   █'
            ],
            'C': [
                '█████',
                '█    ',
                '█    ',
                '█    ',
                '█████'
            ],
            'M': [
                '█   █',
                '██ ██',
                '█ █ █',
                '█   █',
                '█   █'
            ]
        };

        const pattern = patterns[letter];
        if (!pattern) return;

        const startX = centerX - Math.floor(size * 2.5);
        const startY = centerY - Math.floor(size * 2.5);

        for (let row = 0; row < pattern.length; row++) {
            for (let col = 0; col < pattern[row].length; col++) {
                if (pattern[row][col] === '█') {
                    for (let py = 0; py < size; py++) {
                        for (let px = 0; px < size; px++) {
                            this.setPixel(
                                startX + col * size + px,
                                startY + row * size + py,
                                r, g, b, a
                            );
                        }
                    }
                }
            }
        }
    }

    // 生成PNG文件
    toPNG() {
        // PNG文件头
        const signature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);

        // IHDR chunk
        const ihdrData = Buffer.alloc(13);
        ihdrData.writeUInt32BE(this.width, 0);
        ihdrData.writeUInt32BE(this.height, 4);
        ihdrData[8] = 8; // bit depth
        ihdrData[9] = 6; // color type (RGBA)
        ihdrData[10] = 0; // compression
        ihdrData[11] = 0; // filter
        ihdrData[12] = 0; // interlace

        const ihdrChunk = this.createChunk('IHDR', ihdrData);

        // 准备图像数据
        const rowData = Buffer.alloc(this.height * (this.width * 4 + 1));
        for (let y = 0; y < this.height; y++) {
            const rowStart = y * (this.width * 4 + 1);
            rowData[rowStart] = 0; // filter type
            for (let x = 0; x < this.width; x++) {
                const srcIndex = (y * this.width + x) * 4;
                const dstIndex = rowStart + 1 + x * 4;
                rowData[dstIndex] = this.data[srcIndex];     // R
                rowData[dstIndex + 1] = this.data[srcIndex + 1]; // G
                rowData[dstIndex + 2] = this.data[srcIndex + 2]; // B
                rowData[dstIndex + 3] = this.data[srcIndex + 3]; // A
            }
        }

        // 压缩图像数据
        const compressedData = zlib.deflateSync(rowData);
        const idatChunk = this.createChunk('IDAT', compressedData);

        // IEND chunk
        const iendChunk = this.createChunk('IEND', Buffer.alloc(0));

        return Buffer.concat([signature, ihdrChunk, idatChunk, iendChunk]);
    }

    // 创建PNG chunk
    createChunk(type, data) {
        const length = Buffer.alloc(4);
        length.writeUInt32BE(data.length, 0);

        const typeBuffer = Buffer.from(type);
        const crc = this.crc32(Buffer.concat([typeBuffer, data]));
        const crcBuffer = Buffer.alloc(4);
        crcBuffer.writeUInt32BE(crc, 0);

        return Buffer.concat([length, typeBuffer, data, crcBuffer]);
    }

    // CRC32计算
    crc32(data) {
        const table = [];
        for (let i = 0; i < 256; i++) {
            let c = i;
            for (let j = 0; j < 8; j++) {
                c = (c & 1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1);
            }
            table[i] = c;
        }

        let crc = 0xFFFFFFFF;
        for (let i = 0; i < data.length; i++) {
            crc = table[(crc ^ data[i]) & 0xFF] ^ (crc >>> 8);
        }
        return (crc ^ 0xFFFFFFFF) >>> 0;
    }
}

// 解析颜色
function parseColor(colorStr) {
    const hex = colorStr.replace('#', '');
    return {
        r: parseInt(hex.substr(0, 2), 16),
        g: parseInt(hex.substr(2, 2), 16),
        b: parseInt(hex.substr(4, 2), 16)
    };
}

// 生成TabBar图标
function generateTabBarIcon(config) {
    const { size, color, letter, isActive } = config;
    const png = new PNGGenerator(size, size);
    
    // 透明背景
    png.fill(0, 0, 0, 0);
    
    const colorRGB = parseColor(color);
    const centerX = Math.floor(size / 2);
    const centerY = Math.floor(size / 2);
    
    // 绘制背景圆圈（淡色）
    const bgRadius = Math.floor(size * 0.35);
    png.drawCircle(centerX, centerY, bgRadius, colorRGB.r, colorRGB.g, colorRGB.b, isActive ? 40 : 20);
    
    // 绘制字母
    const letterSize = Math.floor(size / 15);
    png.drawLetter(letter, centerX, centerY, letterSize, colorRGB.r, colorRGB.g, colorRGB.b, 255);
    
    return png.toPNG();
}

// TabBar图标配置
const tabBarConfigs = [
    { name: 'tab-home.png', size: 81, color: '#999999', letter: 'H', isActive: false },
    { name: 'tab-home-active.png', size: 81, color: '#FF6B35', letter: 'H', isActive: true },
    { name: 'tab-category.png', size: 81, color: '#999999', letter: 'C', isActive: false },
    { name: 'tab-category-active.png', size: 81, color: '#FF6B35', letter: 'C', isActive: true },
    { name: 'tab-profile.png', size: 81, color: '#999999', letter: 'M', isActive: false },
    { name: 'tab-profile-active.png', size: 81, color: '#FF6B35', letter: 'M', isActive: true }
];

// 自动生成所有TabBar PNG图标
function autoGenerateTabBarPNGs() {
    console.log('🚀 开始自动生成TabBar PNG图标...\n');
    
    let successCount = 0;
    
    tabBarConfigs.forEach(config => {
        try {
            console.log(`📝 生成 ${config.name}...`);
            
            // 生成PNG数据
            const pngData = generateTabBarIcon(config);
            
            // 保存文件
            const filePath = path.join(__dirname, config.name);
            fs.writeFileSync(filePath, pngData);
            
            // 检查文件大小
            const stats = fs.statSync(filePath);
            console.log(`✅ ${config.name} 生成成功 (${stats.size} 字节)`);
            
            successCount++;
        } catch (error) {
            console.error(`❌ ${config.name} 生成失败:`, error.message);
        }
    });
    
    console.log(`\n🎉 生成完成！成功生成 ${successCount}/${tabBarConfigs.length} 个图标`);
    
    if (successCount === tabBarConfigs.length) {
        console.log('\n✅ 所有TabBar图标已成功生成！');
        console.log('📱 现在可以在微信开发者工具中查看TabBar效果了');
    } else {
        console.log('\n⚠️  部分图标生成失败，请检查错误信息');
    }
    
    return successCount === tabBarConfigs.length;
}

// 验证生成的PNG文件
function verifyPNGFiles() {
    console.log('\n🔍 验证生成的PNG文件...\n');
    
    tabBarConfigs.forEach(config => {
        const filePath = path.join(__dirname, config.name);
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            const status = stats.size > 1000 ? '✅ 正常' : '⚠️  可能有问题';
            console.log(`${config.name}: ${stats.size} 字节 - ${status}`);
        } else {
            console.log(`${config.name}: ❌ 文件不存在`);
        }
    });
}

// 执行自动生成
if (require.main === module) {
    try {
        const success = autoGenerateTabBarPNGs();
        verifyPNGFiles();
        
        if (success) {
            console.log('\n🎊 TabBar图标问题已完全解决！');
            console.log('💡 建议现在在微信开发者工具中预览小程序效果');
        }
    } catch (error) {
        console.error('❌ 自动生成过程中出错:', error);
    }
}

module.exports = { autoGenerateTabBarPNGs, verifyPNGFiles };
