
# 图标使用说明

## 已生成的文件

### Tab Bar 图标 (81x81px)
- tab-home.svg / tab-home-active.svg - 首页图标
- tab-category.svg / tab-category-active.svg - 分类图标  
- tab-profile.svg / tab-profile-active.svg - 个人中心图标

### 功能图标 (48x48px)
- icon-search.svg - 搜索图标
- icon-arrow-right.svg / icon-arrow-down.svg - 箭头图标
- icon-star.svg / icon-star-filled.svg - 收藏图标
- icon-share.svg - 分享图标
- icon-download.svg - 下载图标
- icon-points.svg - 积分图标
- icon-close.svg - 关闭图标
- icon-check.svg - 确认图标
- icon-earn.svg - 赚积分图标
- icon-service.svg - 客服图标
- icon-help.svg - 帮助图标

### 年级图标 (120x120px)
- grade-grade_1.svg ~ grade-grade_6.svg - 一到六年级图标

### 占位图片
- default-cover.svg - 默认封面 (400x300px)
- default-avatar.svg - 默认头像 (200x200px)
- empty-materials.svg - 空状态插图 (400x400px)
- empty-favorites.svg - 收藏为空插图 (400x400px)
- empty-downloads.svg - 下载历史为空插图 (400x400px)
- share-cover.svg - 分享封面 (1200x630px)

## 在小程序中使用SVG

小程序支持SVG格式的图片，可以直接使用：

```xml
<image src="/images/icon-search.svg" mode="aspectFit"></image>
```

## 转换为PNG格式

如果需要PNG格式，可以：
1. 使用在线转换工具：https://convertio.co/svg-png/
2. 使用Photoshop或其他图像编辑软件打开SVG并导出PNG
3. 安装Node.js的canvas库进行批量转换

## 自定义图标

如需修改图标，可以：
1. 编辑对应的SVG文件
2. 修改 generate-icons.js 中的配置重新生成
3. 使用专业的图标设计工具
