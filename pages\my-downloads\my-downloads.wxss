/* pages/my-downloads/my-downloads.wxss */
.downloads-container {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 未登录状态 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #fff;
}

.login-prompt image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.prompt-text {
  text-align: center;
  margin-bottom: 40rpx;
}

.prompt-text text {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.login-btn {
  background: #FF6B35;
  color: #fff;
  border-radius: 50rpx;
  padding: 25rpx 60rpx;
  font-size: 28rpx;
  border: none;
}

.login-btn:active {
  background: #E55A2B;
}

/* 统计信息 */
.stats-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.stats-card {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  color: #fff;
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.9;
}

.stats-divider {
  width: 1rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 40rpx;
}

/* 操作栏 */
.toolbar {
  background: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.sort-btn {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 26rpx;
}

.sort-btn image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

.clear-btn {
  color: #FF6B35;
  font-size: 26rpx;
}

/* 排序菜单 */
.sort-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.sort-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.sort-popup {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
}

.sort-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.sort-header text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32rpx;
  height: 32rpx;
}

.close-btn image {
  width: 100%;
  height: 100%;
}

.sort-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 20rpx;
  border-radius: 12rpx;
  background: #f8f8f8;
  transition: all 0.3s ease;
}

.sort-option.active {
  background: rgba(255, 107, 53, 0.1);
  color: #FF6B35;
}

.sort-option text {
  font-size: 28rpx;
}

.sort-option image {
  width: 24rpx;
  height: 24rpx;
}

/* 下载列表 */
.downloads-list {
  background: #fff;
}

.download-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: all 0.3s ease;
}

.download-item:last-child {
  border-bottom: none;
}

.download-item:active {
  background: #f8f8f8;
}

.download-cover {
  width: 120rpx;
  height: 90rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background: #f5f5f5;
}

.download-cover image {
  width: 100%;
  height: 100%;
}

.download-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-right: 20rpx;
}

.download-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.download-meta {
  margin-bottom: 10rpx;
}

.meta-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.download-time {
  font-size: 22rpx;
  color: #999;
}

.download-stats {
  display: flex;
  align-items: center;
}

.points-cost {
  display: flex;
  align-items: center;
  color: #FF6B35;
  font-size: 24rpx;
  font-weight: 500;
}

.points-cost image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}

.download-actions {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  align-items: center;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  min-width: 80rpx;
  transition: all 0.3s ease;
}

.action-btn image {
  width: 28rpx;
  height: 28rpx;
  margin-bottom: 4rpx;
}

.redownload-btn {
  background: rgba(255, 107, 53, 0.1);
  color: #FF6B35;
}

.redownload-btn:active {
  background: rgba(255, 107, 53, 0.2);
}

.delete-btn {
  background: rgba(255, 0, 0, 0.1);
  color: #ff4444;
}

.delete-btn:active {
  background: rgba(255, 0, 0, 0.2);
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
  background: #fff;
}

/* 空状态 */
.empty-downloads {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  background: #fff;
}

.empty-downloads image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  text-align: center;
  color: #999;
  line-height: 1.6;
  margin-bottom: 40rpx;
}

.empty-text text {
  display: block;
  font-size: 26rpx;
}

.browse-btn {
  background: #FF6B35;
  color: #fff;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
  border: none;
}

.browse-btn:active {
  background: #E55A2B;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}