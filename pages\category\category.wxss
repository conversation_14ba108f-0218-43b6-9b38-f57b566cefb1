/* pages/category/category.wxss */
.category-container {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 筛选器区域 */
.filter-section {
  background: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  width: 100%;
  white-space: nowrap;
}

.filter-list {
  display: inline-flex;
  gap: 20rpx;
}

.filter-item {
  display: inline-flex;
  align-items: center;
  padding: 15rpx 25rpx;
  background: #f5f5f5;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-item:active {
  background: #e0e0e0;
}

.filter-item.active {
  background: rgba(255, 107, 53, 0.1);
  color: #FF6B35;
  border: 1rpx solid #FF6B35;
}

.filter-item image {
  width: 20rpx;
  height: 20rpx;
  margin-left: 10rpx;
}

.reset-btn {
  background: #fff !important;
  border: 1rpx solid #ddd !important;
  color: #999 !important;
}

/* 快速分类导航 */
.quick-nav-section {
  padding: 30rpx;
}

.category-group {
  margin-bottom: 40rpx;
}

.category-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 25rpx;
  padding-left: 15rpx;
  border-left: 4rpx solid #FF6B35;
}

.category-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.option-item {
  background: #fff;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.option-item:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.option-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 应用筛选按钮 */
.apply-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.apply-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.filter-content {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 70vh;
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-header text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn image {
  width: 32rpx;
  height: 32rpx;
}

.filter-options {
  max-height: 50vh;
  overflow-y: auto;
  padding: 20rpx 0;
}

.filter-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.filter-option:last-child {
  border-bottom: none;
}

.filter-option:active {
  background: #f8f8f8;
}

.filter-option.selected {
  background: rgba(255, 107, 53, 0.05);
  color: #FF6B35;
}

.filter-option text {
  font-size: 28rpx;
  color: inherit;
}

.filter-option image {
  width: 32rpx;
  height: 32rpx;
}