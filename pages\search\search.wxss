/* pages/search/search.wxss */
.search-container {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 搜索框区域 */
.search-header {
  background: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 80rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.search-icon image {
  width: 100%;
  height: 100%;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
}

.clear-btn image {
  width: 100%;
  height: 100%;
}

.search-btn {
  padding: 20rpx 30rpx;
  background: #FF6B35;
  color: #fff;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.search-btn:active {
  background: #E55A2B;
}

/* 搜索建议区域 */
.search-suggestions {
  background: #fff;
  margin-top: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.clear-history-btn {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 24rpx;
}

.clear-history-btn image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

/* 搜索历史 */
.history-section {
  border-bottom: 20rpx solid #f8f8f8;
}

.history-list {
  padding: 0 30rpx 30rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.history-item:last-child {
  border-bottom: none;
}

.history-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.delete-history-btn {
  width: 32rpx;
  height: 32rpx;
  padding: 10rpx;
}

.delete-history-btn image {
  width: 100%;
  height: 100%;
}

/* 热门搜索 */
.hot-section {
  padding: 30rpx;
}

.hot-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 25rpx;
}

.hot-keyword {
  padding: 15rpx 25rpx;
  background: #f5f5f5;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.hot-keyword:active {
  background: rgba(255, 107, 53, 0.1);
  color: #FF6B35;
}

/* 搜索结果区域 */
.search-results {
  margin-top: 20rpx;
}

.search-status {
  background: #fff;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 26rpx;
  color: #666;
}

.results-list {
  background: #fff;
}

.result-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: all 0.3s ease;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background: #f8f8f8;
}

.result-cover {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 25rpx;
  background: #f5f5f5;
}

.result-cover image {
  width: 100%;
  height: 100%;
}

.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.result-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.result-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.result-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 10rpx;
}

.result-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.result-points {
  color: #FF6B35;
  font-size: 24rpx;
  font-weight: 600;
  white-space: nowrap;
}

.result-stats {
  display: flex;
  gap: 30rpx;
  font-size: 22rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
  background: #fff;
}

/* 空状态 */
.empty-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  background: #fff;
}

.empty-results image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  text-align: center;
  color: #999;
  line-height: 1.6;
}

.empty-text text {
  display: block;
  font-size: 26rpx;
}

/* 搜索中状态 */
.searching-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}