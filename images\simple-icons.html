<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-preview {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
            font-size: 24px;
        }
        .icon-name {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #E55A2B;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .section-title {
            color: #FF6B35;
            border-bottom: 2px solid #FF6B35;
            padding-bottom: 10px;
            margin: 30px 0 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简化图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <p>这个工具使用简单的几何图形和文字创建图标，虽然不如专业图标精美，但可以快速生成所需的图标文件，让你的小程序快速运行起来。</p>
            <p><strong>建议：</strong>先用这些图标完成功能开发，后续再替换为更精美的图标。</p>
        </div>

        <h2 class="section-title">Tab Bar 图标 (81x81px)</h2>
        <div class="icon-grid" id="tab-icons"></div>

        <h2 class="section-title">功能图标 (48x48px)</h2>
        <div class="icon-grid" id="function-icons"></div>

        <h2 class="section-title">年级图标 (120x120px)</h2>
        <div class="icon-grid" id="grade-icons"></div>
    </div>

    <script>
        // 简化图标配置
        const simpleIcons = {
            tab: [
                { name: 'tab-home.png', size: 81, color: '#999', symbol: '🏠', text: '首页' },
                { name: 'tab-home-active.png', size: 81, color: '#FF6B35', symbol: '🏠', text: '首页' },
                { name: 'tab-category.png', size: 81, color: '#999', symbol: '📋', text: '分类' },
                { name: 'tab-category-active.png', size: 81, color: '#FF6B35', symbol: '📋', text: '分类' },
                { name: 'tab-profile.png', size: 81, color: '#999', symbol: '👤', text: '我的' },
                { name: 'tab-profile-active.png', size: 81, color: '#FF6B35', symbol: '👤', text: '我的' }
            ],
            function: [
                { name: 'icon-search.png', size: 48, color: '#666', symbol: '🔍', text: '搜索' },
                { name: 'icon-arrow-right.png', size: 48, color: '#666', symbol: '▶', text: '右箭头' },
                { name: 'icon-arrow-down.png', size: 48, color: '#666', symbol: '▼', text: '下箭头' },
                { name: 'icon-star.png', size: 48, color: '#666', symbol: '☆', text: '收藏' },
                { name: 'icon-star-filled.png', size: 48, color: '#FF6B35', symbol: '★', text: '已收藏' },
                { name: 'icon-share.png', size: 48, color: '#666', symbol: '📤', text: '分享' },
                { name: 'icon-download.png', size: 48, color: '#666', symbol: '📥', text: '下载' },
                { name: 'icon-points.png', size: 48, color: '#FF6B35', symbol: '💰', text: '积分' },
                { name: 'icon-close.png', size: 48, color: '#666', symbol: '✕', text: '关闭' },
                { name: 'icon-check.png', size: 48, color: '#4CAF50', symbol: '✓', text: '确认' },
                { name: 'icon-earn.png', size: 48, color: '#FF6B35', symbol: '💎', text: '赚积分' },
                { name: 'icon-service.png', size: 48, color: '#666', symbol: '🎧', text: '客服' },
                { name: 'icon-help.png', size: 48, color: '#666', symbol: '❓', text: '帮助' }
            ],
            grade: [
                { name: 'grade-grade_1.png', size: 120, color: '#FF6B35', symbol: '1️⃣', text: '一年级' },
                { name: 'grade-grade_2.png', size: 120, color: '#FF6B35', symbol: '2️⃣', text: '二年级' },
                { name: 'grade-grade_3.png', size: 120, color: '#FF6B35', symbol: '3️⃣', text: '三年级' },
                { name: 'grade-grade_4.png', size: 120, color: '#FF6B35', symbol: '4️⃣', text: '四年级' },
                { name: 'grade-grade_5.png', size: 120, color: '#FF6B35', symbol: '5️⃣', text: '五年级' },
                { name: 'grade-grade_6.png', size: 120, color: '#FF6B35', symbol: '6️⃣', text: '六年级' }
            ]
        };

        // 生成简化图标
        function generateSimpleIcon(config) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = config.size;
            canvas.height = config.size;

            // 透明背景
            ctx.clearRect(0, 0, config.size, config.size);

            // 如果是emoji符号，直接绘制
            if (config.symbol.length > 1 && /[\u{1F300}-\u{1F9FF}]/u.test(config.symbol)) {
                ctx.font = `${config.size * 0.6}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(config.symbol, config.size / 2, config.size / 2);
            } else {
                // 绘制简单几何图标
                ctx.fillStyle = config.color;
                ctx.font = `${config.size * 0.5}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(config.symbol, config.size / 2, config.size / 2);
            }

            return canvas;
        }

        // 下载图标
        function downloadIcon(config) {
            const canvas = generateSimpleIcon(config);
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = config.name;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }

        // 生成图标网格
        function generateIconGrid(containerId, icons) {
            const container = document.getElementById(containerId);
            
            icons.forEach(config => {
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                preview.style.color = config.color;
                preview.textContent = config.symbol;
                
                const name = document.createElement('div');
                name.className = 'icon-name';
                name.textContent = config.name;
                
                const btn = document.createElement('button');
                btn.className = 'download-btn';
                btn.textContent = '下载';
                btn.onclick = () => downloadIcon(config);
                
                item.appendChild(preview);
                item.appendChild(name);
                item.appendChild(btn);
                container.appendChild(item);
            });
        }

        // 页面加载完成后生成图标
        document.addEventListener('DOMContentLoaded', function() {
            generateIconGrid('tab-icons', simpleIcons.tab);
            generateIconGrid('function-icons', simpleIcons.function);
            generateIconGrid('grade-icons', simpleIcons.grade);
        });
    </script>
</body>
</html>
