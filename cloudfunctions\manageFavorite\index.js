// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { materialId, action } = event // action: 'add', 'remove', 'check'
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    if (!materialId) {
      return {
        success: false,
        message: '资料ID不能为空'
      }
    }

    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }

    // 查询用户信息
    const userResult = await db.collection('users')
      .where({
        openid: openid
      })
      .get()

    if (!userResult.data || userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userResult.data[0]

    // 检查收藏状态
    const favoriteResult = await db.collection('user_favorites')
      .where({
        user_id: user._id,
        material_id: materialId
      })
      .get()

    const isFavorited = favoriteResult.data && favoriteResult.data.length > 0

    if (action === 'check') {
      // 只检查收藏状态
      return {
        success: true,
        data: {
          isFavorited
        }
      }
    }

    if (action === 'add') {
      // 添加收藏
      if (isFavorited) {
        return {
          success: false,
          message: '已经收藏过了'
        }
      }

      // 检查资料是否存在
      const materialResult = await db.collection('materials')
        .doc(materialId)
        .get()

      if (!materialResult.data) {
        return {
          success: false,
          message: '资料不存在'
        }
      }

      if (!materialResult.data.is_active) {
        return {
          success: false,
          message: '资料已下架'
        }
      }

      // 添加收藏记录
      await db.collection('user_favorites')
        .add({
          data: {
            user_id: user._id,
            material_id: materialId,
            favorite_time: new Date()
          }
        })

      return {
        success: true,
        message: '收藏成功',
        data: {
          isFavorited: true
        }
      }

    } else if (action === 'remove') {
      // 取消收藏
      if (!isFavorited) {
        return {
          success: false,
          message: '还没有收藏过'
        }
      }

      // 删除收藏记录
      await db.collection('user_favorites')
        .where({
          user_id: user._id,
          material_id: materialId
        })
        .remove()

      return {
        success: true,
        message: '取消收藏成功',
        data: {
          isFavorited: false
        }
      }

    } else {
      return {
        success: false,
        message: '无效的操作类型'
      }
    }

  } catch (error) {
    console.error('收藏操作失败:', error)
    return {
      success: false,
      message: '操作失败，请稍后重试'
    }
  }
}
