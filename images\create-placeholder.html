<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>占位图片生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .placeholder-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .placeholder-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #fafafa;
        }
        .placeholder-preview {
            margin: 0 auto 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
            background: #f8f8f8;
        }
        .download-btn {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .download-btn:hover {
            background: #E55A2B;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>占位图片生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <p>点击下方的"生成并下载"按钮可以生成对应尺寸的占位图片，用于小程序开发测试。</p>
        </div>

        <div class="placeholder-grid">
            <div class="placeholder-item">
                <div class="placeholder-preview" style="width: 200px; height: 150px;">
                    默认封面<br>400×300
                </div>
                <div>default-cover.png</div>
                <button class="download-btn" onclick="generatePlaceholder('default-cover.png', 400, 300, '默认封面', '#f0f0f0', '#999')">
                    生成并下载
                </button>
            </div>

            <div class="placeholder-item">
                <div class="placeholder-preview" style="width: 100px; height: 100px; border-radius: 50%;">
                    默认头像<br>200×200
                </div>
                <div>default-avatar.png</div>
                <button class="download-btn" onclick="generateAvatar('default-avatar.png', 200)">
                    生成并下载
                </button>
            </div>

            <div class="placeholder-item">
                <div class="placeholder-preview" style="width: 150px; height: 150px;">
                    空状态插图<br>400×400
                </div>
                <div>empty-materials.png</div>
                <button class="download-btn" onclick="generateEmptyState('empty-materials.png', 400, '暂无资料', '📚')">
                    生成并下载
                </button>
            </div>

            <div class="placeholder-item">
                <div class="placeholder-preview" style="width: 150px; height: 150px;">
                    收藏为空<br>400×400
                </div>
                <div>empty-favorites.png</div>
                <button class="download-btn" onclick="generateEmptyState('empty-favorites.png', 400, '暂无收藏', '⭐')">
                    生成并下载
                </button>
            </div>

            <div class="placeholder-item">
                <div class="placeholder-preview" style="width: 150px; height: 150px;">
                    下载为空<br>400×400
                </div>
                <div>empty-downloads.png</div>
                <button class="download-btn" onclick="generateEmptyState('empty-downloads.png', 400, '暂无下载', '📥')">
                    生成并下载
                </button>
            </div>

            <div class="placeholder-item">
                <div class="placeholder-preview" style="width: 180px; height: 95px;">
                    分享封面<br>1200×630
                </div>
                <div>share-cover.jpg</div>
                <button class="download-btn" onclick="generateShareCover('share-cover.jpg', 1200, 630)">
                    生成并下载
                </button>
            </div>
        </div>
    </div>

    <script>
        // 生成基础占位图片
        function generatePlaceholder(filename, width, height, text, bgColor = '#f0f0f0', textColor = '#999') {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = width;
            canvas.height = height;

            // 背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, width, height);

            // 文字
            ctx.fillStyle = textColor;
            ctx.font = `${Math.min(width, height) / 10}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, width / 2, height / 2);

            // 尺寸信息
            ctx.font = `${Math.min(width, height) / 15}px Arial`;
            ctx.fillText(`${width} × ${height}`, width / 2, height / 2 + Math.min(width, height) / 8);

            downloadCanvas(canvas, filename);
        }

        // 生成头像占位图
        function generateAvatar(filename, size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;

            // 圆形背景
            ctx.fillStyle = '#e0e0e0';
            ctx.beginPath();
            ctx.arc(size / 2, size / 2, size / 2, 0, 2 * Math.PI);
            ctx.fill();

            // 人像图标
            ctx.fillStyle = '#bbb';
            
            // 头部
            ctx.beginPath();
            ctx.arc(size / 2, size * 0.35, size * 0.15, 0, 2 * Math.PI);
            ctx.fill();

            // 身体
            ctx.beginPath();
            ctx.arc(size / 2, size * 0.8, size * 0.25, 0, Math.PI, true);
            ctx.fill();

            downloadCanvas(canvas, filename);
        }

        // 生成空状态插图
        function generateEmptyState(filename, size, text, emoji) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;

            // 透明背景
            ctx.clearRect(0, 0, size, size);

            // 圆形背景
            ctx.fillStyle = 'rgba(255, 107, 53, 0.1)';
            ctx.beginPath();
            ctx.arc(size / 2, size / 2, size * 0.4, 0, 2 * Math.PI);
            ctx.fill();

            // 表情符号
            ctx.font = `${size * 0.3}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(emoji, size / 2, size * 0.4);

            // 文字
            ctx.fillStyle = '#999';
            ctx.font = `${size * 0.08}px Arial`;
            ctx.fillText(text, size / 2, size * 0.7);

            downloadCanvas(canvas, filename);
        }

        // 生成分享封面
        function generateShareCover(filename, width, height) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = width;
            canvas.height = height;

            // 渐变背景
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#FF6B35');
            gradient.addColorStop(1, '#F7931E');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);

            // 标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 80px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('小学教辅资料', width / 2, height / 2 - 50);

            // 副标题
            ctx.font = '40px Arial';
            ctx.fillText('海量优质学习资源', width / 2, height / 2 + 50);

            // 装饰元素
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * width;
                const y = Math.random() * height;
                const radius = Math.random() * 20 + 10;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, 2 * Math.PI);
                ctx.fill();
            }

            downloadCanvas(canvas, filename, 'image/jpeg', 0.8);
        }

        // 下载canvas内容
        function downloadCanvas(canvas, filename, type = 'image/png', quality = 1) {
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, type, quality);
        }
    </script>
</body>
</html>
