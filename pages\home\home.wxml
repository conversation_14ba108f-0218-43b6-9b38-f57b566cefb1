<!--pages/home/<USER>
<view class="home-container">
  <!-- 顶部搜索框 -->
  <view class="search-section">
    <view class="search-box" bindtap="onSearchTap">
      <view class="search-icon">
        <image src="/images/icon-search.svg" mode="aspectFit"></image>
      </view>
      <view class="search-placeholder">搜索试卷、练习册...</view>
    </view>
  </view>

  <!-- 年级分类导航 -->
  <view class="grade-section">
    <view class="section-title">选择年级</view>
    <view class="grade-grid">
      <view 
        class="grade-item" 
        wx:for="{{gradeCategories}}" 
        wx:key="id"
        data-grade="{{item}}"
        bindtap="onGradeTap"
      >
        <view class="grade-icon">
          <image src="/images/grade-{{item.id}}.png" mode="aspectFit"></image>
        </view>
        <view class="grade-name">{{item.name}}</view>
      </view>
    </view>
  </view>

  <!-- 推荐内容区 -->
  <view class="recommend-section">
    <view class="section-header">
      <view class="section-title">最新上传</view>
      <view class="more-btn" bindtap="onMoreRecommendTap">
        <text>更多</text>
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>

    <view class="recommend-content" wx:if="{{!recommendLoading}}">
      <scroll-view class="recommend-scroll" scroll-x="true" show-scrollbar="{{false}}">
        <view class="recommend-list">
          <view 
            class="recommend-item" 
            wx:for="{{recommendMaterials}}" 
            wx:key="_id"
            data-material="{{item}}"
            bindtap="onRecommendTap"
          >
            <view class="material-cover">
              <image
                src="{{item.cover_image || '/images/default-cover.svg'}}"
                mode="aspectFill"
                lazy-load="true"
              ></image>
            </view>
            <view class="material-info">
              <view class="material-title">{{item.title}}</view>
              <view class="material-meta">
                <view class="material-tags">
                  <text class="tag tag-primary" wx:if="{{item.grade_name}}">{{item.grade_name}}</text>
                  <text class="tag tag-secondary" wx:if="{{item.subject_name}}">{{item.subject_name}}</text>
                </view>
                <view class="material-points">{{item.points_cost}}积分</view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 推荐内容加载状态 -->
    <view class="loading" wx:if="{{recommendLoading}}">
      <text>加载中...</text>
    </view>

    <!-- 推荐内容为空 -->
    <view class="empty-state" wx:if="{{!recommendLoading && recommendMaterials.length === 0}}">
      <image src="/images/empty-materials.svg" mode="aspectFit"></image>
      <text>暂无推荐资料</text>
    </view>
  </view>

  <!-- 页面加载状态 -->
  <view class="page-loading" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>
</view>
