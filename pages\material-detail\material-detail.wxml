<!--pages/material-detail/material-detail.wxml-->
<view class="detail-container" wx:if="{{!loading}}">
  <!-- 资料封面和基本信息 -->
  <view class="material-header">
    <view class="cover-section">
      <image
        class="cover-image"
        src="{{material.cover_image || '/images/default-cover.svg'}}"
        mode="aspectFill"
      ></image>
      <view class="cover-overlay">
        <view class="material-type">{{material.project_type_name || '教辅资料'}}</view>
      </view>
    </view>

    <view class="basic-info">
      <view class="material-title">{{material.title}}</view>
      <view class="material-meta">
        <view class="meta-tags">
          <text class="tag tag-primary" wx:if="{{material.grade_name}}">{{material.grade_name}}</text>
          <text class="tag tag-secondary" wx:if="{{material.subject_name}}">{{material.subject_name}}</text>
          <text class="tag tag-secondary" wx:if="{{material.semester_name}}">{{material.semester_name}}</text>
          <text class="tag tag-secondary" wx:if="{{material.textbook_name}}">{{material.textbook_name}}</text>
        </view>
        <view class="material-stats">
          <text>{{material.download_count || 0}}次下载</text>
          <text>{{material.view_count || 0}}次浏览</text>
        </view>
      </view>

      <view class="points-info">
        <view class="points-cost">
          <image src="/images/icon-points.svg" mode="aspectFit"></image>
          <text>{{material.points_cost}}积分</text>
        </view>
        <view class="user-points" wx:if="{{isLogin}}">
          <text>我的积分：{{userInfo.points || 0}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 资料描述 -->
  <view class="description-section" wx:if="{{material.description}}">
    <view class="section-title">资料介绍</view>
    <view class="description-content">{{material.description}}</view>
  </view>

  <!-- 预览图片 -->
  <view class="preview-section" wx:if="{{previewImages.length > 0}}">
    <view class="section-title">资料预览</view>
    <scroll-view class="preview-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="preview-list">
        <view
          class="preview-item"
          wx:for="{{previewImages}}"
          wx:key="*this"
          data-index="{{index}}"
          bindtap="onPreviewImage"
        >
          <image src="{{item}}" mode="aspectFill"></image>
          <view class="preview-mask">
            <image src="/images/icon-search.svg" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 资料详情 -->
  <view class="details-section">
    <view class="section-title">资料详情</view>
    <view class="detail-list">
      <view class="detail-item">
        <text class="detail-label">文件大小</text>
        <text class="detail-value">{{material.file_size || '未知'}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">上传时间</text>
        <text class="detail-value">{{material.upload_time_formatted || material.upload_time}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">文件格式</text>
        <text class="detail-value">{{material.file_format || 'PDF'}}</text>
      </view>
      <view class="detail-item" wx:if="{{material.page_count}}">
        <text class="detail-label">页数</text>
        <text class="detail-value">{{material.page_count}}页</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-section">
    <view class="action-buttons">
      <!-- 收藏按钮 -->
      <view class="action-btn secondary" bindtap="onToggleFavorite" wx:if="{{isLogin}}">
        <image
          src="{{isFavorited ? '/images/icon-star-filled.svg' : '/images/icon-star.svg'}}"
          mode="aspectFit"
        ></image>
        <text>{{isFavorited ? '已收藏' : '收藏'}}</text>
      </view>

      <!-- 分享按钮 -->
      <button class="action-btn secondary" open-type="share">
        <image src="/images/icon-share.svg" mode="aspectFit"></image>
        <text>分享</text>
      </button>

      <!-- 下载按钮 -->
      <view
        class="action-btn primary {{canDownload || isDownloaded ? '' : 'disabled'}}"
        bindtap="onDownload"
      >
        <image src="/images/icon-download.svg" mode="aspectFit"></image>
        <text wx:if="{{downloading}}">下载中...</text>
        <text wx:elif="{{isDownloaded}}">重新下载</text>
        <text wx:elif="{{!isLogin}}">登录下载</text>
        <text wx:elif="{{!canDownload}}">积分不足</text>
        <text wx:else>立即下载</text>
      </view>
    </view>

    <!-- 积分提示 -->
    <view class="points-tip" wx:if="{{!isLogin}}">
      <text>登录后即可下载资料</text>
    </view>
    <view class="points-tip" wx:elif="{{!canDownload && !isDownloaded}}">
      <text>积分不足，需要{{material.points_cost}}积分</text>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section">
    <view class="section-header">
      <view class="section-title">相关推荐</view>
      <view class="more-btn" bindtap="onViewSimilar">
        <text>查看更多</text>
        <image src="/images/icon-arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>
    <!-- 这里可以添加相关资料列表 -->
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="bottom-btn report-btn" bindtap="onReport">
      <text>举报</text>
    </view>
    <view class="bottom-btn similar-btn" bindtap="onViewSimilar">
      <text>同类资料</text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <text>加载中...</text>
</view>