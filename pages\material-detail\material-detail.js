// pages/material-detail/material-detail.js
const { materialAPI, userAPI, pointsAPI } = require('../../utils/api.js')
const { showError, showSuccess, showConfirm, showLoading, hideLoading } = require('../../utils/util.js')

Page({
  data: {
    // 资料信息
    material: null,
    materialId: '',

    // 用户状态
    userInfo: null,
    isLogin: false,

    // 功能状态
    isFavorited: false,
    isDownloaded: false,
    canDownload: false,

    // 页面状态
    loading: true,
    downloading: false,

    // 预览图片
    previewImages: [],
    currentImageIndex: 0,
    showImagePreview: false,

    // 分享信息
    shareInfo: null
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        materialId: options.id
      })
      this.initPage()
    } else {
      showError('资料ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    this.checkUserStatus()
  },

  onShareAppMessage() {
    const material = this.data.material
    if (!material) {
      return {
        title: '小学教辅资料',
        path: '/pages/home/<USER>'
      }
    }

    return {
      title: material.title,
      path: `/pages/material-detail/material-detail?id=${material._id}`,
      imageUrl: material.cover_image || '/images/share-cover.svg'
    }
  },

  // 初始化页面
  async initPage() {
    try {
      showLoading('加载中...')

      // 并行加载数据
      await Promise.all([
        this.loadMaterialDetail(),
        this.checkUserStatus(),
        this.checkFavoriteStatus(),
        this.checkDownloadStatus()
      ])

    } catch (error) {
      console.error('页面初始化失败:', error)
      showError('加载失败，请稍后重试')
    } finally {
      hideLoading()
      this.setData({ loading: false })
    }
  },

  // 加载资料详情
  async loadMaterialDetail() {
    try {
      const material = await materialAPI.getMaterialDetail(this.data.materialId)

      this.setData({
        material,
        previewImages: material.preview_images || []
      })

      // 更新页面标题
      wx.setNavigationBarTitle({
        title: material.title || '资料详情'
      })

      // 检查是否可以下载
      this.checkCanDownload()

    } catch (error) {
      console.error('加载资料详情失败:', error)
      throw error
    }
  },

  // 检查用户状态
  checkUserStatus() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    const isLogin = app.globalData.isLogin

    this.setData({
      userInfo,
      isLogin
    })
  },

  // 检查收藏状态
  async checkFavoriteStatus() {
    if (!this.data.isLogin) return

    try {
      const isFavorited = await materialAPI.checkFavoriteStatus(this.data.materialId)
      this.setData({ isFavorited })
    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  },

  // 检查下载状态
  async checkDownloadStatus() {
    if (!this.data.isLogin) return

    try {
      const isDownloaded = await materialAPI.checkDownloadStatus(this.data.materialId)
      this.setData({ isDownloaded })
    } catch (error) {
      console.error('检查下载状态失败:', error)
    }
  },

  // 检查是否可以下载
  checkCanDownload() {
    const { material, userInfo, isLogin, isDownloaded } = this.data

    if (!material) {
      this.setData({ canDownload: false })
      return
    }

    // 已下载过的可以重新下载
    if (isDownloaded) {
      this.setData({ canDownload: true })
      return
    }

    // 未登录不能下载
    if (!isLogin || !userInfo) {
      this.setData({ canDownload: false })
      return
    }

    // 检查积分是否足够
    const hasEnoughPoints = userInfo.points >= material.points_cost
    this.setData({ canDownload: hasEnoughPoints })
  },

  // 预览图片
  onPreviewImage(e) {
    const { index } = e.currentTarget.dataset
    const images = this.data.previewImages

    if (images && images.length > 0) {
      wx.previewImage({
        urls: images,
        current: images[index] || images[0]
      })
    }
  },

  // 收藏/取消收藏
  async onToggleFavorite() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }

    try {
      const { isFavorited, materialId } = this.data

      if (isFavorited) {
        await materialAPI.removeFavorite(materialId)
        showSuccess('已取消收藏')
        this.setData({ isFavorited: false })
      } else {
        await materialAPI.addFavorite(materialId)
        showSuccess('收藏成功')
        this.setData({ isFavorited: true })
      }

    } catch (error) {
      console.error('收藏操作失败:', error)
      showError('操作失败，请稍后重试')
    }
  },

  // 下载资料
  async onDownload() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }

    const { material, userInfo, isDownloaded, canDownload } = this.data

    if (!canDownload && !isDownloaded) {
      if (userInfo.points < material.points_cost) {
        showError(`积分不足，需要 ${material.points_cost} 积分`)
        return
      }
    }

    // 确认下载
    const confirmText = isDownloaded
      ? '确定要重新下载这个资料吗？'
      : `确定要花费 ${material.points_cost} 积分下载这个资料吗？`

    const confirmed = await showConfirm(confirmText, '确认下载')
    if (!confirmed) return

    try {
      this.setData({ downloading: true })
      showLoading('下载中...')

      const result = await materialAPI.downloadMaterial(this.data.materialId)

      // 更新用户积分（如果是新下载）
      if (!isDownloaded) {
        const app = getApp()
        app.updateUserPoints(result.newBalance)
        this.setData({
          'userInfo.points': result.newBalance,
          isDownloaded: true
        })
      }

      // 保存文件到本地
      if (result.downloadUrl) {
        await this.saveFileToLocal(result.downloadUrl, material.title)
      }

      showSuccess('下载成功')

    } catch (error) {
      console.error('下载失败:', error)
      showError('下载失败，请稍后重试')
    } finally {
      hideLoading()
      this.setData({ downloading: false })
    }
  },

  // 保存文件到本地
  async saveFileToLocal(url, title) {
    try {
      const downloadTask = wx.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            // 保存到相册或文件管理器
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                console.log('文件已保存到相册')
              },
              fail: (error) => {
                console.error('保存到相册失败:', error)
                // 尝试其他保存方式
                this.openDocument(res.tempFilePath)
              }
            })
          }
        },
        fail: (error) => {
          console.error('下载文件失败:', error)
          throw error
        }
      })

      return downloadTask
    } catch (error) {
      console.error('保存文件失败:', error)
      throw error
    }
  },

  // 打开文档
  openDocument(filePath) {
    wx.openDocument({
      filePath: filePath,
      success: () => {
        console.log('文档打开成功')
      },
      fail: (error) => {
        console.error('打开文档失败:', error)
      }
    })
  },

  // 显示登录提示
  async showLoginTip() {
    const confirmed = await showConfirm('请先登录后使用此功能', '提示')
    if (confirmed) {
      wx.switchTab({
        url: '/pages/profile/profile'
      })
    }
  },

  // 查看同类资料
  onViewSimilar() {
    const material = this.data.material
    if (!material || !material.category_ids) return

    // 构建筛选参数
    const params = []
    material.category_ids.forEach(categoryId => {
      if (categoryId.startsWith('grade_')) {
        params.push(`grade=${categoryId}`)
      } else if (['chinese', 'math', 'english', 'science', 'morality'].includes(categoryId)) {
        params.push(`subject=${categoryId}`)
      }
    })

    const filterParams = params.join('&')
    wx.navigateTo({
      url: `/pages/material-list/material-list?${filterParams}&title=同类资料`
    })
  },

  // 举报资料
  async onReport() {
    const options = ['内容不当', '版权问题', '质量问题', '其他问题']

    try {
      const result = await new Promise((resolve) => {
        wx.showActionSheet({
          itemList: options,
          success: (res) => resolve(res.tapIndex),
          fail: () => resolve(-1)
        })
      })

      if (result >= 0) {
        const reason = options[result]
        // 这里可以调用举报API
        showSuccess('举报已提交，感谢您的反馈')
      }
    } catch (error) {
      console.error('举报失败:', error)
    }
  }
})