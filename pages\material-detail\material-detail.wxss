/* pages/material-detail/material-detail.wxss */
.detail-container {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 资料头部 */
.material-header {
  background: #fff;
  margin-bottom: 20rpx;
}

.cover-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 100%);
  display: flex;
  align-items: flex-end;
  padding: 30rpx;
}

.material-type {
  background: rgba(255, 107, 53, 0.9);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.basic-info {
  padding: 30rpx;
}

.material-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.material-meta {
  margin-bottom: 25rpx;
}

.meta-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 15rpx;
}

.material-stats {
  display: flex;
  gap: 30rpx;
  font-size: 24rpx;
  color: #999;
}

.points-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 25rpx;
  background: rgba(255, 107, 53, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 107, 53, 0.2);
}

.points-cost {
  display: flex;
  align-items: center;
  color: #FF6B35;
  font-size: 28rpx;
  font-weight: 600;
}

.points-cost image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.user-points {
  font-size: 24rpx;
  color: #666;
}

/* 通用区块样式 */
.description-section,
.preview-section,
.details-section,
.related-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 25rpx;
  padding-left: 15rpx;
  border-left: 4rpx solid #FF6B35;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.more-btn {
  display: flex;
  align-items: center;
  color: #FF6B35;
  font-size: 26rpx;
}

.more-btn image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

/* 描述内容 */
.description-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 预览图片 */
.preview-scroll {
  width: 100%;
  white-space: nowrap;
}

.preview-list {
  display: inline-flex;
  gap: 20rpx;
}

.preview-item {
  position: relative;
  width: 200rpx;
  height: 150rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f5f5;
}

.preview-item image {
  width: 100%;
  height: 100%;
}

.preview-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preview-item:active .preview-mask {
  opacity: 1;
}

.preview-mask image {
  width: 48rpx;
  height: 48rpx;
}

/* 详情列表 */
.detail-list {
  border-radius: 16rpx;
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  background: #fafafa;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 操作按钮区域 */
.action-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.action-btn image {
  width: 36rpx;
  height: 36rpx;
  margin-bottom: 8rpx;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.action-btn.secondary:active {
  background: #e8e8e8;
}

.action-btn.primary {
  background: #FF6B35;
  color: #fff;
}

.action-btn.primary:active {
  background: #E55A2B;
}

.action-btn.disabled {
  background: #f0f0f0;
  color: #ccc;
  pointer-events: none;
}

.points-tip {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 15rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  z-index: 100;
}

.bottom-btn {
  flex: 1;
  text-align: center;
  padding: 25rpx;
  font-size: 26rpx;
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.bottom-btn:last-child {
  border-right: none;
}

.bottom-btn:active {
  background: #f8f8f8;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}