// pages/home/<USER>
const { userAPI, materialAPI, categoryAPI } = require('../../utils/api.js')
const { showError, debounce } = require('../../utils/util.js')

Page({
  data: {
    // 用户信息
    userInfo: null,
    isLogin: false,
    
    // 年级分类
    gradeCategories: [],
    
    // 推荐资料
    recommendMaterials: [],
    
    // 加载状态
    loading: true,
    recommendLoading: false,
    
    // 搜索关键词
    searchKeyword: ''
  },

  onLoad(options) {
    // 处理分享邀请
    if (options.shareCode) {
      this.handleShareInvite(options.shareCode)
    }
    
    this.initPage()
  },

  onShow() {
    // 更新用户信息
    this.updateUserInfo()
  },

  onPullDownRefresh() {
    this.initPage().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true })
      
      // 并行加载数据
      await Promise.all([
        this.loadGradeCategories(),
        this.loadRecommendMaterials()
      ])
      
    } catch (error) {
      console.error('页面初始化失败:', error)
      showError('页面加载失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 更新用户信息
  updateUserInfo() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    this.setData({
      userInfo,
      isLogin: app.globalData.isLogin
    })
  },

  // 加载年级分类
  async loadGradeCategories() {
    try {
      const categories = await categoryAPI.getCategories()
      const gradeCategory = categories.find(cat => cat._id === 'grade')
      
      if (gradeCategory && gradeCategory.options) {
        // 只显示激活的年级选项
        const activeGrades = gradeCategory.options
          .filter(option => option.is_active)
          .sort((a, b) => a.sort_order - b.sort_order)
        
        this.setData({
          gradeCategories: activeGrades
        })
      }
    } catch (error) {
      console.error('加载年级分类失败:', error)
    }
  },

  // 加载推荐资料
  async loadRecommendMaterials() {
    try {
      this.setData({ recommendLoading: true })
      
      const materials = await materialAPI.getRecommendMaterials(8)
      this.setData({
        recommendMaterials: materials || []
      })
    } catch (error) {
      console.error('加载推荐资料失败:', error)
    } finally {
      this.setData({ recommendLoading: false })
    }
  },

  // 处理分享邀请
  async handleShareInvite(shareCode) {
    try {
      const app = getApp()
      if (!app.globalData.isLogin) {
        // 用户未登录，先登录
        await app.login()
      }
      
      // 处理分享邀请
      await pointsAPI.handleShareInvite(shareCode)
    } catch (error) {
      console.error('处理分享邀请失败:', error)
    }
  },

  // 搜索输入
  onSearchInput: debounce(function(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  }, 300),

  // 点击搜索框
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 搜索提交
  onSearchSubmit() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      showError('请输入搜索关键词')
      return
    }
    
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}`
    })
  },

  // 点击年级分类
  onGradeTap(e) {
    const grade = e.currentTarget.dataset.grade
    if (!grade) return
    
    wx.navigateTo({
      url: `/pages/material-list/material-list?grade=${grade.id}&title=${grade.name}`
    })
  },

  // 点击推荐资料
  onRecommendTap(e) {
    const material = e.currentTarget.dataset.material
    if (!material) return
    
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${material._id}`
    })
  },

  // 查看更多推荐
  onMoreRecommendTap() {
    wx.navigateTo({
      url: '/pages/material-list/material-list?title=推荐资料'
    })
  },

  // 分享页面
  onShareAppMessage() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    
    return {
      title: '小学教辅资料 - 海量优质学习资源',
      path: `/pages/home/<USER>''}` : ''}`,
      imageUrl: '/images/share-cover.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '小学教辅资料 - 海量优质学习资源',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
