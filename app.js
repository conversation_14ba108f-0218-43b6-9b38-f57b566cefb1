// app.js
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        // env 参数说明：
        //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
        //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
        //   如不填则使用默认环境（第一个创建的环境）
        env: 'wx-k12-education', // 云环境ID，需要替换为实际的环境ID
        traceUser: true,
      })
    }

    // 检查用户登录状态
    this.checkLoginStatus()
  },

  onShow() {
    // 小程序显示时的处理
    console.log('小程序显示')
  },

  onHide() {
    // 小程序隐藏时的处理
    console.log('小程序隐藏')
  },

  // 检查用户登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo && userInfo.openid) {
      this.globalData.userInfo = userInfo
      this.globalData.isLogin = true
    } else {
      this.globalData.isLogin = false
    }
  },

  // 用户登录
  async login() {
    try {
      // 调用云函数进行登录
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {}
      })
      
      if (result.result.success) {
        const userInfo = result.result.data
        this.globalData.userInfo = userInfo
        this.globalData.isLogin = true
        
        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', userInfo)
        
        return userInfo
      } else {
        throw new Error(result.result.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
      throw error
    }
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo
  },

  // 更新用户积分
  updateUserPoints(points) {
    if (this.globalData.userInfo) {
      this.globalData.userInfo.points = points
      wx.setStorageSync('userInfo', this.globalData.userInfo)
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLogin: false,
    systemInfo: null
  }
})
